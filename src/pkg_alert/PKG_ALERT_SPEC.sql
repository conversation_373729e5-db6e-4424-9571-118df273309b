CREATE OR REPLACE PACKAGE TEST_1072_4.PKG_ALERT
AS
/* Modification History
****************************************************************************************************
VERSION  WHO          WHEN      WHAT
-------- ------------ --------- --------------------------------------------------------------------
1072.6   Rchatbouri   14/05/25 Mantis 7622: Scheduled Scenario Alerts : JSON Population Error Leading to incorrect Instance Creation
1072.6   Rchatbouri   13/05/25 <PERSON><PERSON> 7664: Alerting: Issue with populating the amounts on alert via smart alert framework
1072.5   Rchatbouri   21/04/25 Mantis 7577: Alert scenarios: Error when columns in the base query exceed a certain number.
1072.5   Rchatbouri   25/03/25 Mantis 7547: Entity Monitor : Alert Icon Displayed in Despite No Active Alerts
1072.5   Rchatbouri   21/03/25 Mantis 7550: Alerting: Improve performance by reducing calls to FN_LAUNCH_SCEN_EVENT
1072.4   Rchatbouri   22/01/25 Mantis 7368: Scenario Maintenance : Unexpected Behavior When Commenting the Last Line of Scenario Queries
1072.4   Rchatbouri   20/01/25 Mantis 7445: Scenario Maintenance : Make Sweep event cannot be performed
1071.4   Rchatbouri   03/07/24 Mantis 7181: Alert Instance summary: Empty grid when role assignement is set to one particual Entity
1070     Rchatbouri   03/08/23 Mantis 6113: INTERNAL: Enhance diagnostics when processing alerts
1070     Rchatbouri   07/04/23 Mantis 6436: Workflow monitor / Alert Summary: zeroes display when not necessary
1069.2   Rchatbouri   20/01/23 Mantis 6417: Alert Scenario Functionality: Resolve various issues
1069     Rchatbouri   14/09/22 Mantis 6259: Character string buffer too small error when creating a scenario with query text longer than 4000 bytes
1068.2   Rchatbouri   10/08/22 Mantis 6112: Predict - Alert Processing - Error handling for a specific scenario event.
1068.2   Rchatbouri   05/08/22 Mantis 6210: Performance improvement in Scenario instance processing
1068.2   Rchatbouri   26/07/22 Mantis 6192: Scenario instance XML Date timestamp fields are having the time part removed
1068     Rchatbouri   20/03/22 Mantis 5028: Extend Alert Scenario functionality to generate alert 'instances' and trigger events
1064     Rchatbouri   12/08/20 Mantis 4923: Predict: Improve performance of common function to get scenario SQL text
1064     RChatbouri   22/06/20 Mantis 4726: Angular Migration: scenario summary screen: Add pFilter parameter to PROC_SCENARIO_COUNT_BY_ENT_CUR
1064     RChatbouri   16/07/20 Mantis 4931: Workflow Monitor: Improve performance of drill-down on scenario SYS_MOV_BACKVAL
1059     S. Chebka    19/12/16 Mantis 3273: Implement email functionality (Phase 2)
1055     RChatbouri   16/05/14 Mantis 2425: Alerting: Processing fails when scenario SQL uses aliases for host entity or currency
1055     Rchatbouri   25/12/13 Mantis 2433: Generic Display Screen: Java heap space error when exporting more than 300 pages
1055     Rchatbouri   08/02/13 Mantis 1443: Enhanced Alerting
****************************************************************************************************
*/

    -- NOT USED YET -- for use with whole movement rows
    --   TYPE REFCUR_MOV_T IS REF CURSOR
    --      RETURN P_MOVEMENT%ROWTYPE;

    /* For output of movement records */
    TYPE T_MOVEMENT_TAB IS TABLE OF P_MOVEMENT%ROWTYPE;

    /* For output of match records */
    TYPE T_MATCH_TAB IS TABLE OF P_MATCH%ROWTYPE;

    /* For processing of scenario query results. Used in FN_GET_P_SCENARIO_COUNT_ROWS */
    TYPE T_P_SCENARIO_COUNTS_TAB IS TABLE OF P_SCENARIO_COUNTS%ROWTYPE;

    /* Limit value used in FN_GET_MOVEMENT_ROWS */
    vLimit NUMBER := 10000;

    TYPE TINDVAL IS TABLE OF VARCHAR2(4000) index by pls_integer;

    TYPE TCOLI IS TABLE OF NUMBER(5) index by pls_integer;

    TYPE REC_SCENARIO_INSTANCE IS RECORD (
        SCENARIO_ID                 P_SCENARIO.SCENARIO_ID%TYPE,
        INSTANCE_UNIQUE_EXPRESSION  P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION%TYPE,
        UNIQUE_IDENTIFIER           P_SCENARIO_INSTANCE.UNIQUE_IDENTIFIER%TYPE,
        HOST_ID                     P_SCENARIO_INSTANCE.HOST_ID%TYPE,
        ENTITY_ID                   P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
        CURRENCY_CODE               P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
        ACCOUNT_ID                  P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE,
        SIGN                        P_SCENARIO_INSTANCE.SIGN%TYPE,
        AMOUNT                      P_SCENARIO_INSTANCE.AMOUNT%TYPE,
        MOVEMENT_ID                 P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE,
        MATCH_ID                    P_SCENARIO_INSTANCE.MATCH_ID%TYPE,
        PAYMENT_ID                  P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE,
        SWEEP_ID                    P_SCENARIO_INSTANCE.SWEEP_ID%TYPE,
        OTHER_ID                    P_SCENARIO_INSTANCE.OTHER_ID%TYPE,
        VALUE_DATE                  P_SCENARIO_INSTANCE.VALUE_DATE%TYPE,
        OVER_THRESHOLD              VARCHAR2(1),
        JSON_ATTRIBUTES              P_SCENARIO_INSTANCE.ATTRIBUTES_JSON%TYPE,
        IS_UNIQUE_ROW               VARCHAR2(1)
    );

    TYPE TAB_SCENARIO_INSTANCE IS TABLE OF REC_SCENARIO_INSTANCE;

    TYPE REC_SCEN_HIGHLIGHT IS RECORD (
        SCENARIO_ID                 P_SCENARIO.SCENARIO_ID%TYPE,
        CATEGORY_ID                 P_SCENARIO.CATEGORY_ID%TYPE,
        CRITICAL_GUI_HIGHLIGHT      P_SCENARIO.CRITICAL_GUI_HIGHLIGHT%TYPE,
        RECORD_SCENARIO_INSTANCES   P_SCENARIO.RECORD_SCENARIO_INSTANCES%TYPE,
        SC_COUNT                    NUMBER
    );

    TYPE TAB_SCEN_HIGHLIGHT IS TABLE OF REC_SCEN_HIGHLIGHT;

    CONST_SYS_USER          CONSTANT VARCHAR2(20) := 'SYS';

    CONST_DATE_FORMAT       CONSTANT VARCHAR2(30) := 'YYYY-MM-DD';
    CONST_DATE_FORMAT_EVENT CONSTANT VARCHAR2(30) := 'YYYY-MM-DD HH24:MI:SS';
    CONST_IGNORE_USE_TYPE   CONSTANT VARCHAR2(20) := 'Ignore';
    CONST_LITERAL_USE_TYPE  CONSTANT VARCHAR2(20) := 'Literal';
    CONST_INSTANCE_ID_ATTR  CONSTANT VARCHAR2(30) := 'INSTANCE_ID';

    CONST_SCEN_INSTANCE_FACILITY  CONSTANT VARCHAR2(30) := 'SCENARIO_INSTANCE_MONITOR';
    CONST_WRKFLW_MONITOR_FACILITY CONSTANT VARCHAR2(30) := 'WORKFLOW_MONITOR';

    CONST_DECIMAL                 CONSTANT CHAR(1) := SUBSTR(TO_CHAR(1.2), 2, 1);

    -- Collection of (NAME,VALUE) pairs
    TYPE TAB_VARCHAR2 IS TABLE OF VARCHAR2(4000) INDEX BY VARCHAR2(50);

    TYPE TAB_VARCHAR IS TABLE OF VARCHAR2(100) INDEX BY PLS_INTEGER;
    TYPE TAB_NUMBER IS TABLE OF NUMBER INDEX BY PLS_INTEGER;
    TYPE TAB_DATE IS TABLE OF DATE INDEX BY PLS_INTEGER;
    TYPE TAB_CLOB IS TABLE OF CLOB INDEX BY PLS_INTEGER;

    TYPE TAB_INT IS TABLE OF INTEGER INDEX BY PLS_INTEGER;
    TYPE TAB_UNIQUE_IDENT IS TABLE OF VARCHAR2(100) INDEX BY VARCHAR2(100);


    /* Get query associated with a scenario */
    FUNCTION FN_GET_QUERY_TEXT (P_ID P_SCENARIO.SCENARIO_ID%TYPE)
      RETURN CLOB;

    FUNCTION FN_GET_CUR (P_SQLTEXT CLOB)
      RETURN SYS_REFCURSOR;

    PROCEDURE PROC_GET_CUR;

    -- Will return select count(*) from scenario query
    -- optional extra conditions can be supplied as second parameter
    FUNCTION FN_GET_COUNT_FOR_QUERY (P_ID            P_SCENARIO.SCENARIO_ID%TYPE,
                                    P_EXTRA_CONDS   VARCHAR2 DEFAULT NULL)
    RETURN NUMBER;

    -- This method gets whole movement row data
    FUNCTION FN_GET_MOVEMENT_ROWS (P_CURSOR IN SYS_REFCURSOR)
      RETURN T_MOVEMENT_TAB
      PIPELINED;

    -- This method gets whole match row data
    FUNCTION FN_GET_MATCH_ROWS (P_CURSOR IN SYS_REFCURSOR)
      RETURN T_MATCH_TAB
      PIPELINED;

    -- This method gets count data in the format of a P_SCENARIO_COUNTS row
    FUNCTION FN_GET_P_SCENARIO_COUNT_ROWS (P_CURSOR IN SYS_REFCURSOR)
    RETURN T_P_SCENARIO_COUNTS_TAB
      PIPELINED;

    --Determine scenario presentation/notification
    -- This function returns the P_NOTIFY_SCENARIO non-key column values
    -- (ACCESS_REQUIRED, DELIVER_POPUPS, FLASH_ICON, SEND_EMAIL) concatenated together:
    FUNCTION FN_GET_SCENARIO_ACCESS (P_SCENARIO_ID    P_NOTIFY_SCENARIO.SCENARIO_ID%TYPE,
                                    P_HOST_ID        P_NOTIFY_SCENARIO.HOST_ID%TYPE,
                                    P_ROLE_ID        P_NOTIFY_SCENARIO.ROLE_ID%TYPE,
                                    P_ENTITY_ID      P_NOTIFY_SCENARIO.ENTITY_ID%TYPE)
      RETURN VARCHAR;

    /* Perform scenario queries and write grouped counts to p_scenario counts */
    PROCEDURE PROC_POPULATE_SCENARIO_COUNTS(vSystemFlag VARCHAR2);

    /* INSERT/Update record on p_scenario counts table */
    PROCEDURE INS_UPD_P_SCENARIO_COUNTS (P_SCENARIO_COUNT_REC P_SCENARIO_COUNTS%ROWTYPE, P_SCENARIO_COUNTS_TAB_BKP T_P_SCENARIO_COUNTS_TAB DEFAULT NULL);

    /* Accept a timestamp interval and convert it to number of seconds */
    FUNCTION FN_CONV_INTERVAL_TO_SECS (P_IN_INTERVAL INTERVAL DAY TO SECOND)
    RETURN NUMBER;

    /*  Accept number of seconds and format it like '1d 15h 46m 39.4832s' */
    FUNCTION FN_CONV_SECS_TO_DUR_STRING (P_IN_SECS NUMBER)
    RETURN VARCHAR2;

    /* RETURN 'Y' IF SUPPLIED TIME IS BETWEEN START AND END TIME*/
    /* IF ANY OF THE PARAMETERS IS NULL RETRUN 'Y' ANYWAY*/
    FUNCTION FN_TEST_START_END_TIME (P_TEST_DATETIME    DATE,
                                    P_START_TIME       VARCHAR2,
                                    P_END_TIME         VARCHAR2)
    RETURN VARCHAR;

    /* Accept a query text, a filter, an order and the two bounds and get the refcursor and the count of rows */
    PROCEDURE PRC_EXEC_QUERY (p_QUERY_TEXT            VARCHAR2,
                              p_FILTER               VARCHAR2,
                              p_ORDER                VARCHAR2,
                              p_ROW_BEGIN            NUMBER,
                              p_ROW_END              NUMBER,
                              p_ASC_DESC             VARCHAR2,
                              p_CUR_RES       IN OUT SYS_REFCURSOR,
                              p_Count_Rows      OUT NUMBER,
                              p_Query_String    OUT VARCHAR2);

    PROCEDURE PROC_SCENARIO_COUNT_BY_ENT_CUR (p_SCENARIO_ID IN P_SCENARIO.SCENARIO_ID%TYPE,
                                             p_ROLE_ID S_ROLE.ROLE_ID%TYPE,
                                             p_THRESHOLD IN CHAR,
                                             p_GroupBy VARCHAR,
                                             p_EntityID IN S_ENTITY.ENTITY_ID%TYPE,
                                             p_SortColumn IN VARCHAR2 DEFAULT NULL,
                                             p_SortDirection IN VARCHAR2 DEFAULT NULL,
                                             p_CcyGroup   IN VARCHAR2,
                                             p_IsAlertable   IN VARCHAR2,
                                             pFilter IN VARCHAR2,
                                             p_CurScenarioMaster IN OUT sys_refcursor,
                                             p_CurScenarioSlave IN OUT sys_refcursor);

    /* Return the counts for each category/scenario for which the user's role has access*/
    PROCEDURE PROC_POPULATE_SCENARIO_CATEG (pRoleID IN S_ROLE.ROLE_ID%TYPE,
                                           pEntityID IN S_ENTITY.ENTITY_ID%TYPE,
                                           pTHRESHOLD IN VARCHAR,
                                           pAlertableOnly IN VARCHAR,
                                           pCcyGroup IN VARCHAR2,
                                           pCallOption IN VARCHAR2,
                                           pSelectedTab IN P_CATEGORY.DISPLAY_TAB%TYPE,
                                           CurScenCountByCateg IN OUT SYS_REFCURSOR,
                                           vCountAllCateg OUT NUMBER);

    -- Return last run date and duration execution for a given scenario
    FUNCTION fn_get_last_run_duration (pSCENARIO_ID IN P_SCENARIO.SCENARIO_ID%TYPE) RETURN VARCHAR2;

    -- Returns a count of alertable scenarios
    PROCEDURE PROC_ALERTABLE_SCENARIO_COUNT(pRoleID IN S_ROLE.ROLE_ID%TYPE,  pTHRESHOLD IN VARCHAR, vCountPopUp OUT NUMBER,  vCountFlash OUT NUMBER,   vCountEmail OUT NUMBER);

    -- Return facility access for a given role
    FUNCTION fn_Get_Facility_Access(p_host_id       in VARCHAR2,
                                 p_role_id         in VARCHAR2,
                                 p_entity_id       in VARCHAR2,
                                 p_currency_code   in VARCHAR2,
                                 p_facility_id     in VARCHAR2) return VARCHAR2;

    -- Return a refcursor of match list for a given scenario, entity and currency
    PROCEDURE proc_get_list_match (p_scenario_id      in VARCHAR2,
                                  p_entity_id        in VARCHAR2,
                                  p_currency_code    in VARCHAR2,
                                  p_threshold        in VARCHAR2,
                                  p_CcyGroup         in VARCHAR2,
                                  p_ref              in out sys_refcursor);

    -- Return the facility menu access for a given role
    FUNCTION fn_get_facility_menu_access ( pHost_id       in VARCHAR2,
                                          pFacility_id   in VARCHAR2,
                                          pRole_id       in VARCHAR2) return VARCHAR2;

    /*
    Execute the generic scenario query and return refcursor of records for a given page
    and return the number of all records for all pages.
    */
    PROCEDURE PRC_EXEC_GENERIC_SCENARIO ( p_Scenario_Id          VARCHAR2,
                                         p_FILTER               VARCHAR2,
                                         p_ORDER                VARCHAR2,
                                         p_ROW_BEGIN            NUMBER,
                                         p_ROW_END              NUMBER,
                                         p_ASC_DESC             VARCHAR2,
                                         p_RoleID               VARCHAR2,
                                         p_CcyGroup             VARCHAR2 DEFAULT NULL,
                                         p_Threshold            VARCHAR2 DEFAULT 'N',
                                         p_CUR_RES       IN OUT SYS_REFCURSOR,
                                         p_Count_Rows      OUT  NUMBER,
                                         p_Query_String    OUT VARCHAR2);
    /*
    This procedure is used to split a string by using a delimiter
    */
    PROCEDURE spSplitter(pSplitString  IN  VARCHAR2,
                        pDeLimiter    IN  VARCHAR2,
                        pMyCur        OUT SYS_REFCURSOR
                       );
    /*
     This function is like function FN_GET_CUR but with two filters to stop unnessary rows being fetched. 105157
    */
    FUNCTION fn_get_cur_curr_entity ( p_sqltext         IN VARCHAR2,
                                     p_currency_code   IN VARCHAR2 DEFAULT NULL,
                                     p_entity_id       IN VARCHAR2)
    RETURN SYS_REFCURSOR;

    FUNCTION FN_GET_SCENARIO_INSTANCE_ROW (P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
                                           P_SCHEDULED_QUERY P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                           P_USER_ID         VARCHAR2  DEFAULT CONST_SYS_USER)
    RETURN TAB_SCENARIO_INSTANCE PIPELINED;

    PROCEDURE SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID       P_SCENARIO_INSTANCE.ID%TYPE,
                                        P_LOG_TEXT                   P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE,
                                        P_LOG_USER                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER);

    PROCEDURE SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID     P_SCENARIO_INSTANCE.ID%TYPE,
                                     PV_NEW_STATUS              P_SCENARIO_INSTANCE.STATUS%TYPE,
                                     P_LOG_TEXT                 P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE,
                                     P_LOG_USER                 P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE,
                                     P_EVENT_STATUS             P_SCENARIO_INSTANCE.EVENTS_LAUNCH_STATUS%TYPE DEFAULT NULL);

    PROCEDURE SP_UPD_SCENARIO_INSTANCE (P_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE,
                                        P_USER_ID               P_SCENARIO_INSTANCE.RESOLVED_BY_USER%TYPE DEFAULT CONST_SYS_USER);

    FUNCTION FN_LAUNCH_SCEN_EVENT (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                  P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
    RETURN NUMBER;

    FUNCTION FN_GET_SCHEDULED_QUERY (P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
                                    P_SCHEDULED_ID    P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE)
    RETURN CLOB;

    PROCEDURE SP_PROCESS_SCENARIO (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                   P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                   P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE dEFAULT NULL,
                                   P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL);

    -- Parallel processing procedures for performance optimization
    PROCEDURE SP_PROCESS_SCENARIO_PARALLEL (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                           P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                           P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                           P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL,
                                           P_CHUNK_SIZE       NUMBER DEFAULT 500,
                                           P_PARALLEL_DEGREE  NUMBER DEFAULT 4);

    PROCEDURE SP_PROCESS_INSTANCES_IN_CHUNKS (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                             P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE,
                                             P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE,
                                             P_CHUNK_SIZE       NUMBER DEFAULT 500);

    PROCEDURE SP_PROCESS_OLD_INSTANCES_PARALLEL (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                                P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE);

    PROCEDURE SP_PROCESS_INSTANCE_CHUNK (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                        P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE,
                                        P_INSTANCES        IN OUT NOCOPY SYS.ANYDATA,
                                        P_COUNT_EVENT_MAP  NUMBER,
                                        P_PROCESSED_COUNT  IN OUT NUMBER,
                                        P_FAILED_COUNT     IN OUT NUMBER);

   FUNCTION FN_GET_XML_INSTANCE_ROW (P_QUERY_TEXT   VARCHAR2)
   RETURN VARCHAR2;

   FUNCTION FN_GET_JSON_INSTANCE_ROW (P_QUERY_TEXT   VARCHAR2)
   RETURN VARCHAR2;

    PROCEDURE SP_PROCESS_ALL_SCENARIOS (P_USER_ID     P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER);

    FUNCTION fn_get_queryresult_as_xml(
                                pv_query_text IN VARCHAR2,
                                pv_date_format IN VARCHAR2 DEFAULT NULL,
                                pv_row_set_tag IN VARCHAR2 DEFAULT 'rowset',
                                pv_row_tag IN VARCHAR2 DEFAULT 'row',
                                max_rows IN NUMBER DEFAULT -1,
                                pv_total_count IN NUMBER DEFAULT -1,
                                pv_additional_qry IN VARCHAR2 DEFAULT NULL)
    RETURN CLOB;

   FUNCTION fn_get_queryresult_as_json(
                                pv_query_text IN CLOB,
                                pv_date_format IN VARCHAR2 DEFAULT NULL,
                                pv_row_set_tag IN VARCHAR2 DEFAULT 'rowset',
                                pv_row_tag IN VARCHAR2 DEFAULT 'row',
                                max_rows IN NUMBER DEFAULT -1,
                                pv_total_count IN NUMBER DEFAULT -1,
                                pv_additional_qry IN VARCHAR2 DEFAULT NULL)
        RETURN CLOB;

    FUNCTION FN_GET_SCN_ATTR_VAL (P_ATTRIBUTE_NAME          VARCHAR2,
                                  P_SCENARIO_INSTANCE_ID    P_SCENARIO_INSTANCE.ID%TYPE)
    RETURN VARCHAR2;

    FUNCTION FN_GET_SCN_ATTR_TYPE (P_ATTRIBUTE_NAME         VARCHAR2,
                                   P_SCENARIO_INSTANCE_ID   P_SCENARIO_INSTANCE.ID%TYPE)
    RETURN VARCHAR2;

    FUNCTION FN_IS_ROW_HIGHLIGHTED (P_FACILITY_ID   P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
                                    P_HOST_ID       P_SCENARIO_INSTANCE.HOST_ID%TYPE,
                                    P_ENTITY_ID     P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
                                    P_CURRENCY_CODE P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
                                    P_VALUE_DATE    DATE,
                                    P_USER_ID       S_USERS.USER_ID%TYPE,
                                    P_CCY_THRESHOLD VARCHAR2 DEFAULT 'N',
                                    P_ACCOUNT_ID    P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
                                    P_MOVEMENT_ID   P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
                                    P_MATCH_ID      P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
                                    P_ILM_GROUP_ID  P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
                                    P_ROLE_ID       S_ROLE.ROLE_ID%TYPE DEFAULT NULL)
    RETURN VARCHAR2;

    FUNCTION FN_GET_HIGHLIGHTING_DETAILS (P_FACILITY_ID     P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
                                          P_HOST_ID         P_SCENARIO_INSTANCE.HOST_ID%TYPE,
                                          P_ENTITY_ID       P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
                                          P_CURRENCY_CODE   P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
                                          P_VALUE_DATE      P_SCENARIO_INSTANCE.VALUE_DATE%TYPE,
                                          P_USER_ID         S_USERS.USER_ID%TYPE,
                                          P_CALL_OPTION     VARCHAR2,
                                          P_SELECTED_TAB    P_CATEGORY.DISPLAY_TAB%TYPE DEFAULT 1,
                                          P_CCY_THRESHOLD   VARCHAR2 DEFAULT 'N',
                                          P_SHOW_ALERT_SCEN VARCHAR2 DEFAULT 'N',
                                          PV_INS_STATUS     VARCHAR2 DEFAULT NULL,
                                          P_RESOL_DATETIME  P_SCENARIO_INSTANCE.RESOLVED_DATETIME%TYPE DEFAULT NULL,
                                          P_ACCOUNT_ID      P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
                                          P_MOVEMENT_ID     P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
                                          P_MATCH_ID        P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
                                          P_ILM_GROUP_ID    P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
                                          P_SWEEP_ID        P_SCENARIO_INSTANCE.SWEEP_ID%TYPE DEFAULT NULL,
                                          P_PAYMENT_ID      P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE DEFAULT NULL)
    RETURN CLOB; --TAB_SCEN_HIGHLIGHT PIPELINED;

    FUNCTION FN_GET_TAB_VAL_IGNORE_QUOTE (P_TAB             TAB_VARCHAR2,
                                          P_INDEX           VARCHAR2)
    RETURN VARCHAR2;

    PROCEDURE SP_CREATE_INSTANCE_API (P_SCENARIO_ID         P_SCENARIO.SCENARIO_ID%TYPE,
                                      P_TAB         TAB_VARCHAR2,
                                      P_USER_ID         VARCHAR2,
                                      P_LOG_TEXT    OUT VARCHAR2,
                                      P_INSTANCE_ID OUT P_SCENARIO_INSTANCE.ID%TYPE);

    FUNCTION FN_GET_SCENARIO_INSTANCES (P_FACILITY_ID     P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
                                         P_HOST_ID         P_SCENARIO_INSTANCE.HOST_ID%TYPE,
                                         P_ENTITY_ID       P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
                                         P_CURRENCY_CODE   P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
                                         P_VALUE_DATE      P_SCENARIO_INSTANCE.VALUE_DATE%TYPE,
                                         P_USER_ID         S_USERS.USER_ID%TYPE,
                                         P_FILTER_TREE     VARCHAR2,
                                         P_SHOW_ALERT_SCEN VARCHAR2 DEFAULT 'N',
                                         PV_INS_STATUS     VARCHAR2 DEFAULT 'All',
                                         P_RESOL_DATETIME  P_SCENARIO_INSTANCE.RESOLVED_DATETIME%TYPE DEFAULT NULL,
                                         P_CCY_THRESHOLD   VARCHAR2 DEFAULT 'N',
                                         P_ACCOUNT_ID      P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
                                         P_MOVEMENT_ID     P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
                                         P_MATCH_ID        P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
                                         P_ILM_GROUP_ID    P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
                                         P_SWEEP_ID        P_SCENARIO_INSTANCE.SWEEP_ID%TYPE DEFAULT NULL,
                                         P_PAYMENT_ID      P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE DEFAULT NULL,
                                         P_FILTER_GRID     VARCHAR2 DEFAULT NULL,
                                         P_SORT_GRID       VARCHAR2 DEFAULT NULL
                                         ) RETURN SYS_REFCURSOR;

    PROCEDURE SP_SET_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                      PV_NEW_STATUS               P_SCENARIO_INSTANCE.STATUS%TYPE,
                                      P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE);

    PROCEDURE SP_UPD_SCEN_INSTANCE_COUNTS (P_SCENARIO_ID        P_SCENARIO.SCENARIO_ID%TYPE);

    FUNCTION FN_EXIST_INST_FOR_SCENARIO (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE)
    RETURN VARCHAR2;

    FUNCTION FN_GET_RESULT_RESOL_QUERY (P_SCENARIO_INSTANCE_ID    P_SCENARIO_INSTANCE.ID%TYPE)
    RETURN NUMBER;

END;
/

