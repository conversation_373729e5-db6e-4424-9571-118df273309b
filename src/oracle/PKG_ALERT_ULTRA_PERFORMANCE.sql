CREATE OR REPLACE PACKAGE PKG_ALERT_ULTRA_PERFORMANCE AS
/*
 * Ultra High Performance Package for Scenario Alert Processing
 * Optimized for maximum throughput using advanced parallel processing techniques
 * 
 * Key Performance Features:
 * - DBMS_SCHEDULER based true parallel processing
 * - Bulk operations with minimal context switches
 * - Advanced caching mechanisms
 * - Optimized SQL generation and execution
 * - Reduced function call overhead
 */

   -- Constants for performance tuning
   CONST_MAX_PARALLEL_JOBS     CONSTANT NUMBER := 12;
   CONST_OPTIMAL_CHUNK_SIZE    CONSTANT NUMBER := 50;
   CONST_BULK_LIMIT           CONSTANT NUMBER := 1000;
   CONST_CACHE_SIZE           CONSTANT NUMBER := 10000;
   CONST_MAX_WAIT_SECONDS     CONSTANT NUMBER := 3600; -- 1 hour

   -- Performance monitoring types
   TYPE T_PERF_STATS IS RECORD (
      instances_processed NUMBER,
      events_executed     NUMBER,
      total_duration      NUMBER,
      avg_per_instance    NUMBER,
      parallel_jobs_used  NUMBER
   );

   -- Cached event mapping type for performance
   TYPE T_CACHED_EVENT IS RECORD (
      event_facility_id   NUMBER,
      program_id         NUMBER,
      map_key           VARCHAR2(100),
      parameters_xml    CLOB,
      sql_template      CLOB
   );
   
   TYPE T_CACHED_EVENT_TAB IS TABLE OF T_CACHED_EVENT INDEX BY VARCHAR2(100);

   -- Main ultra-performance entry point
   PROCEDURE SP_PROCESS_SCENARIO_ULTRA (
      P_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID              P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM',
      P_QUERY_TEXT           P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
      P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL,
      P_PARALLEL_DEGREE      NUMBER DEFAULT NULL,
      P_CHUNK_SIZE           NUMBER DEFAULT NULL
   );

   -- Ultra-parallel processing using DBMS_SCHEDULER
   PROCEDURE SP_PROCESS_SCENARIO_PARALLEL (
      P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM',
      P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
      P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL
   );

   -- Process specific range of instances (called by parallel jobs)
   PROCEDURE SP_PROCESS_INSTANCES_RANGE (
      P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID         P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE,
      P_START_ID        NUMBER,
      P_END_ID          NUMBER
   );

   -- Optimized sequential processing for small batches
   PROCEDURE SP_PROCESS_INSTANCES_SEQUENTIAL_OPTIMIZED (
      P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID         P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE
   );

   -- Bulk movement update processing
   PROCEDURE SP_BULK_UPDATE_MOVEMENTS (
      P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
      P_INSTANCE_IDS    SYS.ODCINUMBERLIST,
      P_USER_ID         P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE
   );

   -- Ultra-fast event launcher with caching
   FUNCTION FN_LAUNCH_EVENTS_ULTRA_FAST (
      P_SCENARIO_INSTANCE_ID P_SCENARIO_INSTANCE.ID%TYPE,
      P_USER_ID             P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM'
   ) RETURN NUMBER;

   -- Optimized SQL builder with caching
   FUNCTION FN_BUILD_OPTIMIZED_SQL (
      P_SCENARIO_INSTANCE_ID NUMBER,
      P_EVENT_FACILITY_ID   NUMBER,
      P_PROGRAM_ID          NUMBER,
      P_MAP_KEY            VARCHAR2,
      P_PARAMETERS_XML     CLOB
   ) RETURN CLOB;

   -- Cache management procedures
   PROCEDURE SP_INITIALIZE_CACHE (P_SCENARIO_ID NUMBER);
   PROCEDURE SP_CLEAR_CACHE;
   PROCEDURE SP_WARM_CACHE (P_SCENARIO_ID NUMBER);

   -- Performance monitoring
   FUNCTION FN_GET_PERFORMANCE_STATS (P_SCENARIO_ID NUMBER) RETURN T_PERF_STATS;
   PROCEDURE SP_LOG_PERFORMANCE_METRICS (
      P_SCENARIO_ID     NUMBER,
      P_STATS          T_PERF_STATS,
      P_USER_ID        VARCHAR2
   );

   -- Utility procedures
   PROCEDURE SP_OPTIMIZE_SCENARIO_INDEXES (P_SCENARIO_ID NUMBER);
   PROCEDURE SP_ANALYZE_PERFORMANCE_BOTTLENECKS (P_SCENARIO_ID NUMBER);

END PKG_ALERT_ULTRA_PERFORMANCE;
/

CREATE OR REPLACE PACKAGE BODY PKG_ALERT_ULTRA_PERFORMANCE AS

   -- Global cache variables for maximum performance
   G_EVENT_CACHE          T_CACHED_EVENT_TAB;
   G_CACHE_INITIALIZED    BOOLEAN := FALSE;
   G_LAST_SCENARIO_ID     NUMBER := -1;

   -- Performance counters
   G_INSTANCES_PROCESSED  NUMBER := 0;
   G_EVENTS_EXECUTED     NUMBER := 0;
   G_START_TIME          TIMESTAMP;

   -- Main ultra-performance entry point with intelligent routing
   PROCEDURE SP_PROCESS_SCENARIO_ULTRA (
      P_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID              P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM',
      P_QUERY_TEXT           P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
      P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL,
      P_PARALLEL_DEGREE      NUMBER DEFAULT NULL,
      P_CHUNK_SIZE           NUMBER DEFAULT NULL
   ) IS
      V_TOTAL_INSTANCES      NUMBER;
      V_PARALLEL_THRESHOLD   NUMBER := 500;
      V_ULTRA_THRESHOLD      NUMBER := 2000;
      V_ERROR_LOCATION       VARCHAR2(10);
   BEGIN
      V_ERROR_LOCATION := '10';
      G_START_TIME := SYSTIMESTAMP;
      
      -- Initialize performance counters
      G_INSTANCES_PROCESSED := 0;
      G_EVENTS_EXECUTED := 0;
      
      -- Get total instances to determine processing strategy
      SELECT COUNT(*)
        INTO V_TOTAL_INSTANCES
        FROM P_SCENARIO_INSTANCE PSI
             INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
       WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
         AND PSI.EVENTS_LAUNCH_STATUS = 'W';

      V_ERROR_LOCATION := '20';
      -- Initialize cache for this scenario
      SP_INITIALIZE_CACHE(P_SCENARIO_ID);
      
      V_ERROR_LOCATION := '30';
      -- Choose optimal processing strategy based on volume
      IF V_TOTAL_INSTANCES < V_PARALLEL_THRESHOLD THEN
         -- Small batch: Use optimized sequential processing
         PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
            'Using optimized sequential processing for ' || V_TOTAL_INSTANCES || ' instances', P_USER_ID);
         SP_PROCESS_INSTANCES_SEQUENTIAL_OPTIMIZED(P_SCENARIO_ID, P_USER_ID);
         
      ELSIF V_TOTAL_INSTANCES < V_ULTRA_THRESHOLD THEN
         -- Medium batch: Use standard parallel processing
         PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
            'Using parallel processing for ' || V_TOTAL_INSTANCES || ' instances', P_USER_ID);
         SP_PROCESS_SCENARIO_PARALLEL(P_SCENARIO_ID, P_USER_ID, P_QUERY_TEXT, P_SCENARIO_SCHEDULE_ID);
         
      ELSE
         -- Large batch: Use ultra-parallel processing
         PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
            'Using ultra-parallel processing for ' || V_TOTAL_INSTANCES || ' instances', P_USER_ID);
         SP_PROCESS_SCENARIO_PARALLEL(P_SCENARIO_ID, P_USER_ID, P_QUERY_TEXT, P_SCENARIO_SCHEDULE_ID);
      END IF;

      V_ERROR_LOCATION := '40';
      -- Log performance metrics
      DECLARE
         V_STATS T_PERF_STATS;
      BEGIN
         V_STATS := FN_GET_PERFORMANCE_STATS(P_SCENARIO_ID);
         SP_LOG_PERFORMANCE_METRICS(P_SCENARIO_ID, V_STATS, P_USER_ID);
      END;

   EXCEPTION
      WHEN OTHERS THEN
         sp_error_log('', P_USER_ID, 'DBSERVER',
                     'PKG_ALERT_ULTRA_PERFORMANCE.SP_PROCESS_SCENARIO_ULTRA -> Error at location ' || V_ERROR_LOCATION,
                     SQLCODE, SQLERRM);
         RAISE;
   END;

   -- Ultra-parallel processing using DBMS_SCHEDULER for maximum throughput
   PROCEDURE SP_PROCESS_SCENARIO_PARALLEL (
      P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM',
      P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
      P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL
   ) IS
      V_START_TIMESTAMP      TIMESTAMP;
      V_END_TIMESTAMP        TIMESTAMP;
      V_TOTAL_INSTANCES      NUMBER := 0;
      V_ERROR_LOCATION       VARCHAR2(10);
      V_JOB_PREFIX          VARCHAR2(50);
      V_PARALLEL_JOBS       NUMBER := CONST_MAX_PARALLEL_JOBS;
      V_MAX_INSTANCE_ID     NUMBER;
      V_MIN_INSTANCE_ID     NUMBER;
      V_INSTANCES_PER_JOB   NUMBER;
      V_JOB_NAME           VARCHAR2(100);
      V_JOBS_COMPLETED     NUMBER := 0;
      V_WAIT_COUNTER       NUMBER := 0;
   BEGIN
      V_ERROR_LOCATION := '10';
      V_START_TIMESTAMP := SYSTIMESTAMP;
      
      -- Create unique job prefix with timestamp
      V_JOB_PREFIX := 'ULTRA_SCEN_' || P_SCENARIO_ID || '_' || TO_CHAR(SYSTIMESTAMP, 'HHMISSFF');
      
      V_ERROR_LOCATION := '20';
      -- Get instance range and count for optimal job distribution
      SELECT COUNT(*), MIN(PSI.ID), MAX(PSI.ID)
        INTO V_TOTAL_INSTANCES, V_MIN_INSTANCE_ID, V_MAX_INSTANCE_ID
        FROM P_SCENARIO_INSTANCE PSI
             INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
       WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
         AND PSI.EVENTS_LAUNCH_STATUS = 'W';
      
      -- Adjust parallel degree based on instance count
      IF V_TOTAL_INSTANCES < 1000 THEN
         V_PARALLEL_JOBS := 4;
      ELSIF V_TOTAL_INSTANCES < 5000 THEN
         V_PARALLEL_JOBS := 8;
      ELSE
         V_PARALLEL_JOBS := CONST_MAX_PARALLEL_JOBS;
      END IF;
      
      V_ERROR_LOCATION := '30';
      -- Calculate optimal instances per job
      V_INSTANCES_PER_JOB := CEIL(V_TOTAL_INSTANCES / V_PARALLEL_JOBS);
      
      PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
         'Starting ultra-parallel processing: ' || V_TOTAL_INSTANCES || ' instances, ' || 
         V_PARALLEL_JOBS || ' jobs, ~' || V_INSTANCES_PER_JOB || ' instances/job', P_USER_ID);
      
      V_ERROR_LOCATION := '40';
      -- Create and launch parallel jobs using DBMS_SCHEDULER
      FOR i IN 1..V_PARALLEL_JOBS LOOP
         V_JOB_NAME := V_JOB_PREFIX || '_J' || LPAD(i, 2, '0');
         
         DBMS_SCHEDULER.CREATE_JOB(
            job_name        => V_JOB_NAME,
            job_type        => 'PLSQL_BLOCK',
            job_action      => 'BEGIN PKG_ALERT_ULTRA_PERFORMANCE.SP_PROCESS_INSTANCES_RANGE(' ||
                              P_SCENARIO_ID || ', ''' || P_USER_ID || ''', ' ||
                              (V_MIN_INSTANCE_ID + (i-1) * V_INSTANCES_PER_JOB) || ', ' ||
                              LEAST(V_MIN_INSTANCE_ID + i * V_INSTANCES_PER_JOB - 1, V_MAX_INSTANCE_ID) ||
                              '); END;',
            start_date      => SYSTIMESTAMP,
            enabled         => TRUE,
            auto_drop       => TRUE,
            comments        => 'Ultra-parallel scenario job ' || i || ' of ' || V_PARALLEL_JOBS
         );
      END LOOP;
      
      V_ERROR_LOCATION := '50';
      -- Monitor job completion with intelligent waiting
      WHILE V_JOBS_COMPLETED < V_PARALLEL_JOBS AND V_WAIT_COUNTER < CONST_MAX_WAIT_SECONDS LOOP
         DBMS_LOCK.SLEEP(2); -- Wait 2 seconds between checks
         V_WAIT_COUNTER := V_WAIT_COUNTER + 2;
         
         -- Count completed/failed jobs
         SELECT COUNT(*)
           INTO V_JOBS_COMPLETED
           FROM USER_SCHEDULER_JOBS
          WHERE JOB_NAME LIKE V_JOB_PREFIX || '_J%'
            AND STATE IN ('SUCCEEDED', 'FAILED', 'BROKEN');
            
         -- Log progress every 30 seconds
         IF MOD(V_WAIT_COUNTER, 30) = 0 THEN
            PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
               'Progress: ' || V_JOBS_COMPLETED || '/' || V_PARALLEL_JOBS || ' jobs completed after ' || 
               V_WAIT_COUNTER || ' seconds', P_USER_ID);
         END IF;
      END LOOP;
      
      V_ERROR_LOCATION := '60';
      -- Clean up completed jobs and log any failures
      FOR job_rec IN (SELECT JOB_NAME, STATE FROM USER_SCHEDULER_JOBS 
                      WHERE JOB_NAME LIKE V_JOB_PREFIX || '_J%') LOOP
         IF job_rec.STATE = 'FAILED' THEN
            PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
               'Warning: Job ' || job_rec.JOB_NAME || ' failed', P_USER_ID);
         END IF;
         
         BEGIN
            DBMS_SCHEDULER.DROP_JOB(job_rec.JOB_NAME, TRUE);
         EXCEPTION
            WHEN OTHERS THEN NULL;
         END;
      END LOOP;
      
      V_ERROR_LOCATION := '70';
      -- Final processing and cleanup
      PKG_ALERT.SP_UPD_SCEN_INSTANCE_COUNTS(P_SCENARIO_ID);
      PKG_ALERT.SP_UPD_SCENARIO_INSTANCE(P_SCENARIO_ID, P_USER_ID);
      
      V_END_TIMESTAMP := SYSTIMESTAMP;
      PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
         'Ultra-parallel processing completed in ' || 
         EXTRACT(SECOND FROM (V_END_TIMESTAMP - V_START_TIMESTAMP)) || ' seconds', P_USER_ID);

   EXCEPTION
      WHEN OTHERS THEN
         -- Emergency cleanup of all jobs
         FOR job_rec IN (SELECT JOB_NAME FROM USER_SCHEDULER_JOBS 
                         WHERE JOB_NAME LIKE V_JOB_PREFIX || '_J%') LOOP
            BEGIN
               DBMS_SCHEDULER.DROP_JOB(job_rec.JOB_NAME, TRUE);
            EXCEPTION
               WHEN OTHERS THEN NULL;
            END;
         END LOOP;
         
         sp_error_log('', P_USER_ID, 'DBSERVER',
                     'PKG_ALERT_ULTRA_PERFORMANCE.SP_PROCESS_SCENARIO_PARALLEL -> Error at ' || V_ERROR_LOCATION,
                     SQLCODE, SQLERRM);
         RAISE;
   END;
