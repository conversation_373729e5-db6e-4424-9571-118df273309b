CREATE OR REPLACE PACKAGE PKG_ALERT_ULTRA_PERFORMANCE AS
/*
 * Ultra High Performance Package for Scenario Alert Processing
 * Optimized for maximum throughput using advanced parallel processing techniques
 * 
 * Key Performance Features:
 * - DBMS_SCHEDULER based true parallel processing
 * - Bulk operations with minimal context switches
 * - Advanced caching mechanisms
 * - Optimized SQL generation and execution
 * - Reduced function call overhead
 */

   -- Constants for performance tuning
   CONST_MAX_PARALLEL_JOBS     CONSTANT NUMBER := 12;
   CONST_OPTIMAL_CHUNK_SIZE    CONSTANT NUMBER := 50;
   CONST_BULK_LIMIT           CONSTANT NUMBER := 1000;
   CONST_CACHE_SIZE           CONSTANT NUMBER := 10000;
   CONST_MAX_WAIT_SECONDS     CONSTANT NUMBER := 3600; -- 1 hour

   -- Performance monitoring types
   TYPE T_PERF_STATS IS RECORD (
      instances_processed NUMBER,
      events_executed     NUMBER,
      total_duration      NUMBER,
      avg_per_instance    NUMBER,
      parallel_jobs_used  NUMBER
   );

   -- Cached event mapping type for performance
   TYPE T_CACHED_EVENT IS RECORD (
      event_facility_id   NUMBER,
      program_id         NUMBER,
      map_key           VARCHAR2(100),
      parameters_xml    CLOB,
      sql_template      CLOB
   );
   
   TYPE T_CACHED_EVENT_TAB IS TABLE OF T_CACHED_EVENT INDEX BY VARCHAR2(100);

   -- Main ultra-performance entry point
   PROCEDURE SP_PROCESS_SCENARIO_ULTRA (
      P_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID              P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM',
      P_QUERY_TEXT           P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
      P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL,
      P_PARALLEL_DEGREE      NUMBER DEFAULT NULL,
      P_CHUNK_SIZE           NUMBER DEFAULT NULL
   );

   -- Ultra-parallel processing using DBMS_SCHEDULER
   PROCEDURE SP_PROCESS_SCENARIO_PARALLEL (
      P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM',
      P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
      P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL
   );

   -- Process specific range of instances (called by parallel jobs)
   PROCEDURE SP_PROCESS_INSTANCES_RANGE (
      P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID         P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE,
      P_START_ID        NUMBER,
      P_END_ID          NUMBER
   );

   -- Optimized sequential processing for small batches
   PROCEDURE SP_PROCESS_INSTANCES_SEQUENTIAL_OPTIMIZED (
      P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID         P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE
   );

   -- Bulk movement update processing
   PROCEDURE SP_BULK_UPDATE_MOVEMENTS (
      P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
      P_INSTANCE_IDS    SYS.ODCINUMBERLIST,
      P_USER_ID         P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE
   );

   -- Ultra-fast event launcher with caching
   FUNCTION FN_LAUNCH_EVENTS_ULTRA_FAST (
      P_SCENARIO_INSTANCE_ID P_SCENARIO_INSTANCE.ID%TYPE,
      P_USER_ID             P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM'
   ) RETURN NUMBER;

   -- Optimized SQL builder with caching
   FUNCTION FN_BUILD_OPTIMIZED_SQL (
      P_SCENARIO_INSTANCE_ID NUMBER,
      P_EVENT_FACILITY_ID   NUMBER,
      P_PROGRAM_ID          NUMBER,
      P_MAP_KEY            VARCHAR2,
      P_PARAMETERS_XML     CLOB
   ) RETURN CLOB;

   -- Cache management procedures
   PROCEDURE SP_INITIALIZE_CACHE (P_SCENARIO_ID NUMBER);
   PROCEDURE SP_CLEAR_CACHE;
   PROCEDURE SP_WARM_CACHE (P_SCENARIO_ID NUMBER);

   -- Performance monitoring
   FUNCTION FN_GET_PERFORMANCE_STATS (P_SCENARIO_ID NUMBER) RETURN T_PERF_STATS;
   PROCEDURE SP_LOG_PERFORMANCE_METRICS (
      P_SCENARIO_ID     NUMBER,
      P_STATS          T_PERF_STATS,
      P_USER_ID        VARCHAR2
   );

   -- Utility procedures
   PROCEDURE SP_OPTIMIZE_SCENARIO_INDEXES (P_SCENARIO_ID NUMBER);
   PROCEDURE SP_ANALYZE_PERFORMANCE_BOTTLENECKS (P_SCENARIO_ID NUMBER);

END PKG_ALERT_ULTRA_PERFORMANCE;
/

CREATE OR REPLACE PACKAGE BODY PKG_ALERT_ULTRA_PERFORMANCE AS

   -- Global cache variables for maximum performance
   G_EVENT_CACHE          T_CACHED_EVENT_TAB;
   G_CACHE_INITIALIZED    BOOLEAN := FALSE;
   G_LAST_SCENARIO_ID     NUMBER := -1;

   -- Performance counters
   G_INSTANCES_PROCESSED  NUMBER := 0;
   G_EVENTS_EXECUTED     NUMBER := 0;
   G_START_TIME          TIMESTAMP;

   -- Main ultra-performance entry point with intelligent routing
   PROCEDURE SP_PROCESS_SCENARIO_ULTRA (
      P_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID              P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM',
      P_QUERY_TEXT           P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
      P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL,
      P_PARALLEL_DEGREE      NUMBER DEFAULT NULL,
      P_CHUNK_SIZE           NUMBER DEFAULT NULL
   ) IS
      V_TOTAL_INSTANCES      NUMBER;
      V_PARALLEL_THRESHOLD   NUMBER := 500;
      V_ULTRA_THRESHOLD      NUMBER := 2000;
      V_ERROR_LOCATION       VARCHAR2(10);
   BEGIN
      V_ERROR_LOCATION := '10';
      G_START_TIME := SYSTIMESTAMP;
      
      -- Initialize performance counters
      G_INSTANCES_PROCESSED := 0;
      G_EVENTS_EXECUTED := 0;
      
      -- Get total instances to determine processing strategy
      SELECT COUNT(*)
        INTO V_TOTAL_INSTANCES
        FROM P_SCENARIO_INSTANCE PSI
             INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
       WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
         AND PSI.EVENTS_LAUNCH_STATUS = 'W';

      V_ERROR_LOCATION := '20';
      -- Initialize cache for this scenario
      SP_INITIALIZE_CACHE(P_SCENARIO_ID);
      
      V_ERROR_LOCATION := '30';
      -- Choose optimal processing strategy based on volume
      IF V_TOTAL_INSTANCES < V_PARALLEL_THRESHOLD THEN
         -- Small batch: Use optimized sequential processing
         PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
            'Using optimized sequential processing for ' || V_TOTAL_INSTANCES || ' instances', P_USER_ID);
         SP_PROCESS_INSTANCES_SEQUENTIAL_OPTIMIZED(P_SCENARIO_ID, P_USER_ID);
         
      ELSIF V_TOTAL_INSTANCES < V_ULTRA_THRESHOLD THEN
         -- Medium batch: Use standard parallel processing
         PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
            'Using parallel processing for ' || V_TOTAL_INSTANCES || ' instances', P_USER_ID);
         SP_PROCESS_SCENARIO_PARALLEL(P_SCENARIO_ID, P_USER_ID, P_QUERY_TEXT, P_SCENARIO_SCHEDULE_ID);
         
      ELSE
         -- Large batch: Use ultra-parallel processing
         PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
            'Using ultra-parallel processing for ' || V_TOTAL_INSTANCES || ' instances', P_USER_ID);
         SP_PROCESS_SCENARIO_PARALLEL(P_SCENARIO_ID, P_USER_ID, P_QUERY_TEXT, P_SCENARIO_SCHEDULE_ID);
      END IF;

      V_ERROR_LOCATION := '40';
      -- Log performance metrics
      DECLARE
         V_STATS T_PERF_STATS;
      BEGIN
         V_STATS := FN_GET_PERFORMANCE_STATS(P_SCENARIO_ID);
         SP_LOG_PERFORMANCE_METRICS(P_SCENARIO_ID, V_STATS, P_USER_ID);
      END;

   EXCEPTION
      WHEN OTHERS THEN
         sp_error_log('', P_USER_ID, 'DBSERVER',
                     'PKG_ALERT_ULTRA_PERFORMANCE.SP_PROCESS_SCENARIO_ULTRA -> Error at location ' || V_ERROR_LOCATION,
                     SQLCODE, SQLERRM);
         RAISE;
   END;

   -- Ultra-parallel processing using DBMS_SCHEDULER for maximum throughput
   PROCEDURE SP_PROCESS_SCENARIO_PARALLEL (
      P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM',
      P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
      P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL
   ) IS
      V_START_TIMESTAMP      TIMESTAMP;
      V_END_TIMESTAMP        TIMESTAMP;
      V_TOTAL_INSTANCES      NUMBER := 0;
      V_ERROR_LOCATION       VARCHAR2(10);
      V_JOB_PREFIX          VARCHAR2(50);
      V_PARALLEL_JOBS       NUMBER := CONST_MAX_PARALLEL_JOBS;
      V_MAX_INSTANCE_ID     NUMBER;
      V_MIN_INSTANCE_ID     NUMBER;
      V_INSTANCES_PER_JOB   NUMBER;
      V_JOB_NAME           VARCHAR2(100);
      V_JOBS_COMPLETED     NUMBER := 0;
      V_WAIT_COUNTER       NUMBER := 0;
   BEGIN
      V_ERROR_LOCATION := '10';
      V_START_TIMESTAMP := SYSTIMESTAMP;
      
      -- Create unique job prefix with timestamp
      V_JOB_PREFIX := 'ULTRA_SCEN_' || P_SCENARIO_ID || '_' || TO_CHAR(SYSTIMESTAMP, 'HHMISSFF');
      
      V_ERROR_LOCATION := '20';
      -- Get instance range and count for optimal job distribution
      SELECT COUNT(*), MIN(PSI.ID), MAX(PSI.ID)
        INTO V_TOTAL_INSTANCES, V_MIN_INSTANCE_ID, V_MAX_INSTANCE_ID
        FROM P_SCENARIO_INSTANCE PSI
             INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
       WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
         AND PSI.EVENTS_LAUNCH_STATUS = 'W';
      
      -- Adjust parallel degree based on instance count
      IF V_TOTAL_INSTANCES < 1000 THEN
         V_PARALLEL_JOBS := 4;
      ELSIF V_TOTAL_INSTANCES < 5000 THEN
         V_PARALLEL_JOBS := 8;
      ELSE
         V_PARALLEL_JOBS := CONST_MAX_PARALLEL_JOBS;
      END IF;
      
      V_ERROR_LOCATION := '30';
      -- Calculate optimal instances per job
      V_INSTANCES_PER_JOB := CEIL(V_TOTAL_INSTANCES / V_PARALLEL_JOBS);
      
      PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
         'Starting ultra-parallel processing: ' || V_TOTAL_INSTANCES || ' instances, ' || 
         V_PARALLEL_JOBS || ' jobs, ~' || V_INSTANCES_PER_JOB || ' instances/job', P_USER_ID);
      
      V_ERROR_LOCATION := '40';
      -- Create and launch parallel jobs using DBMS_SCHEDULER
      FOR i IN 1..V_PARALLEL_JOBS LOOP
         V_JOB_NAME := V_JOB_PREFIX || '_J' || LPAD(i, 2, '0');
         
         DBMS_SCHEDULER.CREATE_JOB(
            job_name        => V_JOB_NAME,
            job_type        => 'PLSQL_BLOCK',
            job_action      => 'BEGIN PKG_ALERT_ULTRA_PERFORMANCE.SP_PROCESS_INSTANCES_RANGE(' ||
                              P_SCENARIO_ID || ', ''' || P_USER_ID || ''', ' ||
                              (V_MIN_INSTANCE_ID + (i-1) * V_INSTANCES_PER_JOB) || ', ' ||
                              LEAST(V_MIN_INSTANCE_ID + i * V_INSTANCES_PER_JOB - 1, V_MAX_INSTANCE_ID) ||
                              '); END;',
            start_date      => SYSTIMESTAMP,
            enabled         => TRUE,
            auto_drop       => TRUE,
            comments        => 'Ultra-parallel scenario job ' || i || ' of ' || V_PARALLEL_JOBS
         );
      END LOOP;
      
      V_ERROR_LOCATION := '50';
      -- Monitor job completion with intelligent waiting
      WHILE V_JOBS_COMPLETED < V_PARALLEL_JOBS AND V_WAIT_COUNTER < CONST_MAX_WAIT_SECONDS LOOP
         DBMS_LOCK.SLEEP(2); -- Wait 2 seconds between checks
         V_WAIT_COUNTER := V_WAIT_COUNTER + 2;
         
         -- Count completed/failed jobs
         SELECT COUNT(*)
           INTO V_JOBS_COMPLETED
           FROM USER_SCHEDULER_JOBS
          WHERE JOB_NAME LIKE V_JOB_PREFIX || '_J%'
            AND STATE IN ('SUCCEEDED', 'FAILED', 'BROKEN');
            
         -- Log progress every 30 seconds
         IF MOD(V_WAIT_COUNTER, 30) = 0 THEN
            PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
               'Progress: ' || V_JOBS_COMPLETED || '/' || V_PARALLEL_JOBS || ' jobs completed after ' || 
               V_WAIT_COUNTER || ' seconds', P_USER_ID);
         END IF;
      END LOOP;
      
      V_ERROR_LOCATION := '60';
      -- Clean up completed jobs and log any failures
      FOR job_rec IN (SELECT JOB_NAME, STATE FROM USER_SCHEDULER_JOBS 
                      WHERE JOB_NAME LIKE V_JOB_PREFIX || '_J%') LOOP
         IF job_rec.STATE = 'FAILED' THEN
            PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
               'Warning: Job ' || job_rec.JOB_NAME || ' failed', P_USER_ID);
         END IF;
         
         BEGIN
            DBMS_SCHEDULER.DROP_JOB(job_rec.JOB_NAME, TRUE);
         EXCEPTION
            WHEN OTHERS THEN NULL;
         END;
      END LOOP;
      
      V_ERROR_LOCATION := '70';
      -- Final processing and cleanup
      PKG_ALERT.SP_UPD_SCEN_INSTANCE_COUNTS(P_SCENARIO_ID);
      PKG_ALERT.SP_UPD_SCENARIO_INSTANCE(P_SCENARIO_ID, P_USER_ID);
      
      V_END_TIMESTAMP := SYSTIMESTAMP;
      PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL, 
         'Ultra-parallel processing completed in ' || 
         EXTRACT(SECOND FROM (V_END_TIMESTAMP - V_START_TIMESTAMP)) || ' seconds', P_USER_ID);

   EXCEPTION
      WHEN OTHERS THEN
         -- Emergency cleanup of all jobs
         FOR job_rec IN (SELECT JOB_NAME FROM USER_SCHEDULER_JOBS 
                         WHERE JOB_NAME LIKE V_JOB_PREFIX || '_J%') LOOP
            BEGIN
               DBMS_SCHEDULER.DROP_JOB(job_rec.JOB_NAME, TRUE);
            EXCEPTION
               WHEN OTHERS THEN NULL;
            END;
         END LOOP;
         
         sp_error_log('', P_USER_ID, 'DBSERVER',
                     'PKG_ALERT_ULTRA_PERFORMANCE.SP_PROCESS_SCENARIO_PARALLEL -> Error at ' || V_ERROR_LOCATION,
                     SQLCODE, SQLERRM);
         RAISE;
   END;

   -- Process specific range of instances with maximum efficiency
   PROCEDURE SP_PROCESS_INSTANCES_RANGE (
      P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID         P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE,
      P_START_ID        NUMBER,
      P_END_ID          NUMBER
   ) IS
      TYPE T_INSTANCE_ID_TAB IS TABLE OF NUMBER;
      V_INSTANCE_IDS    T_INSTANCE_ID_TAB;
      V_PROCESSED_COUNT NUMBER := 0;
      V_FAILED_COUNT    NUMBER := 0;
      V_RESULT          NUMBER;
   BEGIN
      -- Bulk collect instance IDs in range for maximum efficiency
      SELECT PSI.ID
        BULK COLLECT INTO V_INSTANCE_IDS
        FROM P_SCENARIO_INSTANCE PSI
             INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
       WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
         AND PSI.ID BETWEEN P_START_ID AND P_END_ID
         AND PSI.EVENTS_LAUNCH_STATUS = 'W'
       ORDER BY PSI.ID;

      -- Process instances using ultra-fast event launcher
      FOR i IN 1..V_INSTANCE_IDS.COUNT LOOP
         BEGIN
            V_RESULT := FN_LAUNCH_EVENTS_ULTRA_FAST(V_INSTANCE_IDS(i), P_USER_ID);

            IF V_RESULT = 1 THEN
               V_PROCESSED_COUNT := V_PROCESSED_COUNT + 1;
               UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'L' WHERE ID = V_INSTANCE_IDS(i);
            ELSE
               V_FAILED_COUNT := V_FAILED_COUNT + 1;
               UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = V_INSTANCE_IDS(i);
            END IF;

            -- Commit every 50 instances to avoid long transactions
            IF MOD(i, 50) = 0 THEN
               COMMIT;
            END IF;

         EXCEPTION
            WHEN OTHERS THEN
               V_FAILED_COUNT := V_FAILED_COUNT + 1;
               sp_error_log('', P_USER_ID, 'DBSERVER',
                           'Error processing instance ' || V_INSTANCE_IDS(i), SQLCODE, SQLERRM);
         END;
      END LOOP;

      COMMIT; -- Final commit for this range
      G_INSTANCES_PROCESSED := G_INSTANCES_PROCESSED + V_PROCESSED_COUNT;

   EXCEPTION
      WHEN OTHERS THEN
         ROLLBACK;
         sp_error_log('', P_USER_ID, 'DBSERVER',
                     'PKG_ALERT_ULTRA_PERFORMANCE.SP_PROCESS_INSTANCES_RANGE -> Range ' ||
                     P_START_ID || '-' || P_END_ID, SQLCODE, SQLERRM);
         RAISE;
   END;

   -- Optimized sequential processing for small batches
   PROCEDURE SP_PROCESS_INSTANCES_SEQUENTIAL_OPTIMIZED (
      P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
      P_USER_ID         P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE
   ) IS
      TYPE T_INSTANCE_ID_TAB IS TABLE OF NUMBER;
      V_INSTANCE_IDS    T_INSTANCE_ID_TAB;
      V_BATCH_SIZE      NUMBER := 100;
      V_PROCESSED       NUMBER := 0;
      V_FAILED          NUMBER := 0;
      V_RESULT          NUMBER;
   BEGIN
      -- Get all instances that need processing
      SELECT PSI.ID
        BULK COLLECT INTO V_INSTANCE_IDS
        FROM P_SCENARIO_INSTANCE PSI
             INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
       WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
         AND PSI.EVENTS_LAUNCH_STATUS = 'W'
       ORDER BY PSI.ID;

      -- Process in optimized batches
      FOR i IN 1..V_INSTANCE_IDS.COUNT LOOP
         BEGIN
            V_RESULT := FN_LAUNCH_EVENTS_ULTRA_FAST(V_INSTANCE_IDS(i), P_USER_ID);

            IF V_RESULT = 1 THEN
               V_PROCESSED := V_PROCESSED + 1;
               UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'L' WHERE ID = V_INSTANCE_IDS(i);
            ELSE
               V_FAILED := V_FAILED + 1;
               UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = V_INSTANCE_IDS(i);
            END IF;

            -- Batch commit for performance
            IF MOD(i, V_BATCH_SIZE) = 0 THEN
               COMMIT;
            END IF;

         EXCEPTION
            WHEN OTHERS THEN
               V_FAILED := V_FAILED + 1;
               UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = V_INSTANCE_IDS(i);
         END;
      END LOOP;

      COMMIT;

      PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL,
         'Sequential processing completed: ' || V_PROCESSED || ' processed, ' || V_FAILED || ' failed', P_USER_ID);

   EXCEPTION
      WHEN OTHERS THEN
         ROLLBACK;
         sp_error_log('', P_USER_ID, 'DBSERVER',
                     'PKG_ALERT_ULTRA_PERFORMANCE.SP_PROCESS_INSTANCES_SEQUENTIAL_OPTIMIZED',
                     SQLCODE, SQLERRM);
         RAISE;
   END;

   -- Ultra-fast event launcher with advanced caching and optimization
   FUNCTION FN_LAUNCH_EVENTS_ULTRA_FAST (
      P_SCENARIO_INSTANCE_ID P_SCENARIO_INSTANCE.ID%TYPE,
      P_USER_ID             P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT 'SYSTEM'
   ) RETURN NUMBER IS
      V_RETURN_RESULT       NUMBER := 1;
      V_NBR_FAILED_EVENTS   NUMBER := 0;
      V_SQL                 CLOB;

      -- Optimized cursor for events
      CURSOR C_EVENTS IS
         SELECT SEF.EVENT_FACILITY_ID, SEF.PROGRAM_ID, SEM.MAP_KEY, SEM.PARAMETERS_XML,
                SEF.EXECUTE_WHEN, SEF.REPEAT_ON_RERAISE, PSI.RESOLVED_DATETIME
           FROM P_SCENARIO_EVENT_FACILITY SEF
                INNER JOIN P_SCENARIO_EVENT_MAPPING SEM ON SEF.EVENT_FACILITY_ID = SEM.EVENT_FACILITY_ID
                INNER JOIN P_SCENARIO_INSTANCE PSI ON SEM.SCENARIO_ID = PSI.SCENARIO_ID
          WHERE PSI.ID = P_SCENARIO_INSTANCE_ID
          ORDER BY SEF.EVENT_ORDER;
   BEGIN
      -- Process each event for this instance
      FOR event_rec IN C_EVENTS LOOP
         BEGIN
            -- Check execution conditions
            IF (    (V_NBR_FAILED_EVENTS > 0 AND event_rec.EXECUTE_WHEN = 'E')
                 OR (V_NBR_FAILED_EVENTS = 0 AND event_rec.EXECUTE_WHEN = 'S')
                 OR (NVL(event_rec.EXECUTE_WHEN, 'A') = 'A'))
                AND (   event_rec.RESOLVED_DATETIME IS NULL
                    OR (event_rec.REPEAT_ON_RERAISE = 'Y' AND event_rec.RESOLVED_DATETIME IS NOT NULL))
            THEN
               -- Build optimized SQL using cache
               V_SQL := FN_BUILD_OPTIMIZED_SQL(
                  P_SCENARIO_INSTANCE_ID,
                  event_rec.EVENT_FACILITY_ID,
                  event_rec.PROGRAM_ID,
                  event_rec.MAP_KEY,
                  event_rec.PARAMETERS_XML
               );

               -- Execute the SQL if generated successfully
               IF V_SQL IS NOT NULL THEN
                  EXECUTE IMMEDIATE V_SQL;
                  G_EVENTS_EXECUTED := G_EVENTS_EXECUTED + 1;
               ELSE
                  V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
               END IF;
            END IF;

         EXCEPTION
            WHEN OTHERS THEN
               V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
               PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(P_SCENARIO_INSTANCE_ID,
                  'Error executing event ' || event_rec.EVENT_FACILITY_ID || ': ' || SQLERRM, P_USER_ID);
         END;
      END LOOP;

      -- Return success/failure status
      IF V_NBR_FAILED_EVENTS > 0 THEN
         V_RETURN_RESULT := -1;
      END IF;

      RETURN V_RETURN_RESULT;

   EXCEPTION
      WHEN OTHERS THEN
         PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(P_SCENARIO_INSTANCE_ID,
            'Critical error in ultra-fast launcher: ' || SQLERRM, P_USER_ID);
         RETURN -1;
   END;

   -- Optimized SQL builder with caching for maximum performance
   FUNCTION FN_BUILD_OPTIMIZED_SQL (
      P_SCENARIO_INSTANCE_ID NUMBER,
      P_EVENT_FACILITY_ID   NUMBER,
      P_PROGRAM_ID          NUMBER,
      P_MAP_KEY            VARCHAR2,
      P_PARAMETERS_XML     CLOB
   ) RETURN CLOB IS
      V_SQL                CLOB;
      V_CACHE_KEY         VARCHAR2(100);
      V_MOVEMENT_ID       NUMBER;
      V_SQL_PART1         VARCHAR2(4000);
      V_SQL_PART2         VARCHAR2(4000);
      V_COL_AUTO_COL      VARCHAR2(4000) := 'MOVEMENT_ID,INPUT_DATE,';
      V_COL_AUTO_VAL      VARCHAR2(4000) := 'P_MOVEMENT_SEQUENCE.NEXTVAL,GLOBAL_VAR.SYS_DATE,';
   BEGIN
      -- Create cache key for this SQL pattern
      V_CACHE_KEY := P_EVENT_FACILITY_ID || '_' || P_PROGRAM_ID || '_' || P_MAP_KEY;

      -- Check if we have cached SQL for this pattern
      IF G_EVENT_CACHE.EXISTS(V_CACHE_KEY) THEN
         -- Use cached template and substitute instance-specific values
         V_SQL := G_EVENT_CACHE(V_CACHE_KEY).sql_template;
         V_SQL := REPLACE(V_SQL, '${INSTANCE_ID}', P_SCENARIO_INSTANCE_ID);

         -- For movement updates, get the movement ID
         IF P_PROGRAM_ID = 6 THEN -- Update Movement
            V_MOVEMENT_ID := TO_NUMBER(PKG_ALERT.FN_GET_SCN_ATTR_VAL('MOVEMENT_ID', P_SCENARIO_INSTANCE_ID));
            V_SQL := REPLACE(V_SQL, '${MOVEMENT_ID}', V_MOVEMENT_ID);
         END IF;

         RETURN V_SQL;
      END IF;

      -- Build SQL from scratch and cache it
      CASE P_PROGRAM_ID
         WHEN 5 THEN -- Insert Movement
            V_SQL := FN_BUILD_INSERT_MOVEMENT_SQL_OPTIMIZED(P_SCENARIO_INSTANCE_ID, P_EVENT_FACILITY_ID,
                                                           P_MAP_KEY, P_PARAMETERS_XML, V_COL_AUTO_COL, V_COL_AUTO_VAL);
         WHEN 6 THEN -- Update Movement
            V_SQL := FN_BUILD_UPDATE_MOVEMENT_SQL_OPTIMIZED(P_SCENARIO_INSTANCE_ID, P_EVENT_FACILITY_ID,
                                                           P_MAP_KEY, P_PARAMETERS_XML, V_COL_AUTO_COL, V_COL_AUTO_VAL);
         WHEN 12 THEN -- Make Sweep
            V_SQL := FN_BUILD_SWEEP_SQL_OPTIMIZED(P_SCENARIO_INSTANCE_ID, P_EVENT_FACILITY_ID, P_MAP_KEY, P_PARAMETERS_XML);
         WHEN 19 THEN -- Insert/Update Balance
            V_SQL := FN_BUILD_BALANCE_SQL_OPTIMIZED(P_SCENARIO_INSTANCE_ID, P_EVENT_FACILITY_ID,
                                                   P_MAP_KEY, P_PARAMETERS_XML, V_COL_AUTO_COL, V_COL_AUTO_VAL);
         ELSE
            -- Use original PKG_ALERT function for other program types
            RETURN PKG_ALERT.FN_LAUNCH_SCEN_EVENT(P_SCENARIO_INSTANCE_ID);
      END CASE;

      -- Cache the SQL template for future use
      IF V_SQL IS NOT NULL AND NOT G_EVENT_CACHE.EXISTS(V_CACHE_KEY) THEN
         G_EVENT_CACHE(V_CACHE_KEY).event_facility_id := P_EVENT_FACILITY_ID;
         G_EVENT_CACHE(V_CACHE_KEY).program_id := P_PROGRAM_ID;
         G_EVENT_CACHE(V_CACHE_KEY).map_key := P_MAP_KEY;
         G_EVENT_CACHE(V_CACHE_KEY).parameters_xml := P_PARAMETERS_XML;
         G_EVENT_CACHE(V_CACHE_KEY).sql_template := V_SQL;
      END IF;

      RETURN V_SQL;

   EXCEPTION
      WHEN OTHERS THEN
         sp_error_log('', 'SYSTEM', 'DBSERVER',
                     'PKG_ALERT_ULTRA_PERFORMANCE.FN_BUILD_OPTIMIZED_SQL -> Error for instance ' || P_SCENARIO_INSTANCE_ID,
                     SQLCODE, SQLERRM);
         RETURN NULL;
   END;

   -- Ultra-optimized UPDATE movement SQL builder
   FUNCTION FN_BUILD_UPDATE_MOVEMENT_SQL_OPTIMIZED (
      P_SCENARIO_INSTANCE_ID  NUMBER,
      P_EVENT_FACILITY_ID     NUMBER,
      P_MAP_KEY               VARCHAR2,
      P_PARAMETERS_XML        CLOB,
      P_COL_AUTO_COL          VARCHAR2,
      P_COL_AUTO_VAL          VARCHAR2
   ) RETURN CLOB IS
      V_SQL_PART1         VARCHAR2(4000);
      V_SQL_PART2         VARCHAR2(4000);
      V_MOVEMENT_ID       NUMBER;
      V_SQL               CLOB;
   BEGIN
      -- Get movement ID efficiently
      V_MOVEMENT_ID := TO_NUMBER(PKG_ALERT.FN_GET_SCN_ATTR_VAL('MOVEMENT_ID', P_SCENARIO_INSTANCE_ID));

      -- Build column and value parts using optimized XML parsing
      SELECT LISTAGG(EVENT.MAP_COL, ',') WITHIN GROUP (ORDER BY EVENT.MAP_COL),
             LISTAGG(CASE WHEN EVENT.DATA_TYPE = 'VARCHAR2' THEN '''' || EVENT.LITERAL_VAL || ''''
                          ELSE EVENT.LITERAL_VAL END, ',') WITHIN GROUP (ORDER BY EVENT.MAP_COL)
        INTO V_SQL_PART1, V_SQL_PART2
        FROM P_SCENARIO_EVENT_MAPPING MAP
             INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
             CROSS JOIN XMLTABLE('/mappedParameters/parameter'
                                PASSING XMLTYPE(MAP.PARAMETERS_XML)
                                COLUMNS MAP_COL VARCHAR2(128) path 'name',
                                        DATA_TYPE VARCHAR2(30) path 'data_type',
                                        LITERAL_VAL VARCHAR2(128) path 'value'
                                ) event
       WHERE MAP.EVENT_FACILITY_ID = P_EVENT_FACILITY_ID
         AND MAP.MAP_KEY = P_MAP_KEY
         AND PSI.ID = P_SCENARIO_INSTANCE_ID
         AND EVENT.LITERAL_VAL IS NOT NULL;

      -- Build optimized UPDATE statement
      V_SQL := 'BEGIN UPDATE P_MOVEMENT SET (' ||
               P_COL_AUTO_COL || V_SQL_PART1 ||
               ') = (SELECT ' ||
               P_COL_AUTO_VAL || V_SQL_PART2 ||
               ' FROM DUAL) WHERE MOVEMENT_ID = ' || V_MOVEMENT_ID ||
               '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (' || P_SCENARIO_INSTANCE_ID ||
               ', ''Movement updated: '' || ' || V_MOVEMENT_ID || '); END;';

      RETURN V_SQL;

   EXCEPTION
      WHEN OTHERS THEN
         RETURN NULL;
   END;

   -- Cache management procedures for optimal performance
   PROCEDURE SP_INITIALIZE_CACHE (P_SCENARIO_ID NUMBER) IS
   BEGIN
      -- Clear cache if scenario changed
      IF G_LAST_SCENARIO_ID != P_SCENARIO_ID THEN
         SP_CLEAR_CACHE;
         G_LAST_SCENARIO_ID := P_SCENARIO_ID;
      END IF;

      -- Warm up cache with common event mappings
      SP_WARM_CACHE(P_SCENARIO_ID);
      G_CACHE_INITIALIZED := TRUE;
   END;

   PROCEDURE SP_CLEAR_CACHE IS
   BEGIN
      G_EVENT_CACHE.DELETE;
      G_CACHE_INITIALIZED := FALSE;
   END;

   PROCEDURE SP_WARM_CACHE (P_SCENARIO_ID NUMBER) IS
      CURSOR C_EVENT_MAPPINGS IS
         SELECT DISTINCT SEM.EVENT_FACILITY_ID, SEF.PROGRAM_ID, SEM.MAP_KEY, SEM.PARAMETERS_XML
           FROM P_SCENARIO_EVENT_MAPPING SEM
                INNER JOIN P_SCENARIO_EVENT_FACILITY SEF ON SEM.EVENT_FACILITY_ID = SEF.EVENT_FACILITY_ID
          WHERE SEM.SCENARIO_ID = P_SCENARIO_ID;
   BEGIN
      -- Pre-load common event mappings into cache
      FOR mapping_rec IN C_EVENT_MAPPINGS LOOP
         DECLARE
            V_CACHE_KEY VARCHAR2(100);
            V_DUMMY_SQL CLOB;
         BEGIN
            V_CACHE_KEY := mapping_rec.EVENT_FACILITY_ID || '_' || mapping_rec.PROGRAM_ID || '_' || mapping_rec.MAP_KEY;

            -- Build and cache SQL template
            V_DUMMY_SQL := FN_BUILD_OPTIMIZED_SQL(
               -1, -- Dummy instance ID for template
               mapping_rec.EVENT_FACILITY_ID,
               mapping_rec.PROGRAM_ID,
               mapping_rec.MAP_KEY,
               mapping_rec.PARAMETERS_XML
            );
         EXCEPTION
            WHEN OTHERS THEN NULL; -- Ignore errors during cache warming
         END;
      END LOOP;
   END;

   -- Performance monitoring functions
   FUNCTION FN_GET_PERFORMANCE_STATS (P_SCENARIO_ID NUMBER) RETURN T_PERF_STATS IS
      V_STATS T_PERF_STATS;
      V_DURATION NUMBER;
   BEGIN
      V_DURATION := EXTRACT(SECOND FROM (SYSTIMESTAMP - G_START_TIME));

      V_STATS.instances_processed := G_INSTANCES_PROCESSED;
      V_STATS.events_executed := G_EVENTS_EXECUTED;
      V_STATS.total_duration := V_DURATION;
      V_STATS.avg_per_instance := CASE WHEN G_INSTANCES_PROCESSED > 0
                                       THEN V_DURATION / G_INSTANCES_PROCESSED
                                       ELSE 0 END;
      V_STATS.parallel_jobs_used := CONST_MAX_PARALLEL_JOBS;

      RETURN V_STATS;
   END;

   PROCEDURE SP_LOG_PERFORMANCE_METRICS (
      P_SCENARIO_ID     NUMBER,
      P_STATS          T_PERF_STATS,
      P_USER_ID        VARCHAR2
   ) IS
   BEGIN
      PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(NULL,
         'PERFORMANCE METRICS - Scenario: ' || P_SCENARIO_ID ||
         ', Instances: ' || P_STATS.instances_processed ||
         ', Events: ' || P_STATS.events_executed ||
         ', Duration: ' || ROUND(P_STATS.total_duration, 2) || 's' ||
         ', Avg/Instance: ' || ROUND(P_STATS.avg_per_instance, 3) || 's' ||
         ', Throughput: ' || ROUND(P_STATS.instances_processed / NULLIF(P_STATS.total_duration, 0), 1) || ' inst/sec',
         P_USER_ID);
   END;

   -- Stub functions for INSERT movement and other operations (implement as needed)
   FUNCTION FN_BUILD_INSERT_MOVEMENT_SQL_OPTIMIZED (
      P_SCENARIO_INSTANCE_ID  NUMBER,
      P_EVENT_FACILITY_ID     NUMBER,
      P_MAP_KEY               VARCHAR2,
      P_PARAMETERS_XML        CLOB,
      P_COL_AUTO_COL          VARCHAR2,
      P_COL_AUTO_VAL          VARCHAR2
   ) RETURN CLOB IS
   BEGIN
      -- Use original PKG_ALERT function for now - can be optimized later
      RETURN PKG_ALERT.FN_LAUNCH_SCEN_EVENT(P_SCENARIO_INSTANCE_ID);
   END;

   FUNCTION FN_BUILD_SWEEP_SQL_OPTIMIZED (
      P_SCENARIO_INSTANCE_ID  NUMBER,
      P_EVENT_FACILITY_ID     NUMBER,
      P_MAP_KEY               VARCHAR2,
      P_PARAMETERS_XML        CLOB
   ) RETURN CLOB IS
   BEGIN
      -- Use original PKG_ALERT function for now - can be optimized later
      RETURN PKG_ALERT.FN_LAUNCH_SCEN_EVENT(P_SCENARIO_INSTANCE_ID);
   END;

   FUNCTION FN_BUILD_BALANCE_SQL_OPTIMIZED (
      P_SCENARIO_INSTANCE_ID  NUMBER,
      P_EVENT_FACILITY_ID     NUMBER,
      P_MAP_KEY               VARCHAR2,
      P_PARAMETERS_XML        CLOB,
      P_COL_AUTO_COL          VARCHAR2,
      P_COL_AUTO_VAL          VARCHAR2
   ) RETURN CLOB IS
   BEGIN
      -- Use original PKG_ALERT function for now - can be optimized later
      RETURN PKG_ALERT.FN_LAUNCH_SCEN_EVENT(P_SCENARIO_INSTANCE_ID);
   END;

   -- Bulk movement update processing (placeholder for future enhancement)
   PROCEDURE SP_BULK_UPDATE_MOVEMENTS (
      P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
      P_INSTANCE_IDS    SYS.ODCINUMBERLIST,
      P_USER_ID         P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE
   ) IS
   BEGIN
      -- Future enhancement: Implement bulk FORALL operations
      FOR i IN 1..P_INSTANCE_IDS.COUNT LOOP
         DECLARE
            V_RESULT NUMBER;
         BEGIN
            V_RESULT := FN_LAUNCH_EVENTS_ULTRA_FAST(P_INSTANCE_IDS(i), P_USER_ID);
         EXCEPTION
            WHEN OTHERS THEN NULL;
         END;
      END LOOP;
   END;

END PKG_ALERT_ULTRA_PERFORMANCE;
/
