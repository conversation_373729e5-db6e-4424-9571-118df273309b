-- Configuration table for parallel processing parameters
-- This allows dynamic configuration without code changes

-- Create configuration table if it doesn't exist
CREATE TABLE P_SCENARIO_PARALLEL_CONFIG (
    CONFIG_KEY VARCHAR2(50) PRIMARY KEY,
    CONFIG_VALUE VARCHAR2(100) NOT NULL,
    DESCRIPTION VARCHAR2(500),
    CREATED_DATE DATE DEFAULT SYSDATE,
    MOD<PERSON>IED_DATE DATE DEFAULT SYSDATE
);

-- Insert default configuration values
INSERT INTO P_SCENARIO_PARALLEL_CONFIG (CONFIG_KEY, CONFIG_VALUE, DESCRIPTION) 
VALUES ('PARALLEL_THRESHOLD', '1000', 'Minimum number of instances to trigger parallel processing');

INSERT INTO P_SCENARIO_PARALLEL_CONFIG (CONFIG_KEY, CONFIG_VALUE, DESCRIPTION) 
VALUES ('DEFAULT_CHUNK_SIZE', '500', 'Default chunk size for parallel processing');

INSERT INTO P_SCENARIO_PARALLEL_CONFIG (CONF<PERSON>_KEY, CONFIG_VALUE, DESCRIPTION) 
VALUES ('DEFAULT_PARALLEL_DEGREE', '4', 'Default parallel degree for processing');

INSERT INTO P_SCENARIO_PARALLEL_CONFIG (CONFIG_KEY, CONFIG_VALUE, DESCRIPTION) 
VALUES ('MAX_CHUNK_SIZE', '2000', 'Maximum allowed chunk size');

INSERT INTO P_SCENARIO_PARALLEL_CONFIG (CONFIG_KEY, CONFIG_VALUE, DESCRIPTION) 
VALUES ('MAX_PARALLEL_DEGREE', '8', 'Maximum allowed parallel degree');

INSERT INTO P_SCENARIO_PARALLEL_CONFIG (CONFIG_KEY, CONFIG_VALUE, DESCRIPTION) 
VALUES ('ENABLE_PARALLEL_PROCESSING', 'Y', 'Enable/disable parallel processing (Y/N)');

INSERT INTO P_SCENARIO_PARALLEL_CONFIG (CONFIG_KEY, CONFIG_VALUE, DESCRIPTION) 
VALUES ('COMMIT_FREQUENCY', '100', 'Number of instances to process before commit in chunk processing');

COMMIT;

-- Create function to get configuration values
CREATE OR REPLACE FUNCTION FN_GET_PARALLEL_CONFIG(P_CONFIG_KEY VARCHAR2) 
RETURN VARCHAR2
IS
    V_CONFIG_VALUE VARCHAR2(100);
BEGIN
    SELECT CONFIG_VALUE 
    INTO V_CONFIG_VALUE 
    FROM P_SCENARIO_PARALLEL_CONFIG 
    WHERE CONFIG_KEY = P_CONFIG_KEY;
    
    RETURN V_CONFIG_VALUE;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        -- Return default values for known keys
        CASE P_CONFIG_KEY
            WHEN 'PARALLEL_THRESHOLD' THEN RETURN '1000';
            WHEN 'DEFAULT_CHUNK_SIZE' THEN RETURN '500';
            WHEN 'DEFAULT_PARALLEL_DEGREE' THEN RETURN '4';
            WHEN 'ENABLE_PARALLEL_PROCESSING' THEN RETURN 'Y';
            WHEN 'COMMIT_FREQUENCY' THEN RETURN '100';
            ELSE RETURN NULL;
        END CASE;
    WHEN OTHERS THEN
        RETURN NULL;
END;
/

-- Create procedure to update configuration
CREATE OR REPLACE PROCEDURE SP_UPDATE_PARALLEL_CONFIG(
    P_CONFIG_KEY VARCHAR2,
    P_CONFIG_VALUE VARCHAR2
)
IS
BEGIN
    UPDATE P_SCENARIO_PARALLEL_CONFIG 
    SET CONFIG_VALUE = P_CONFIG_VALUE,
        MODIFIED_DATE = SYSDATE
    WHERE CONFIG_KEY = P_CONFIG_KEY;
    
    IF SQL%ROWCOUNT = 0 THEN
        INSERT INTO P_SCENARIO_PARALLEL_CONFIG (CONFIG_KEY, CONFIG_VALUE, DESCRIPTION)
        VALUES (P_CONFIG_KEY, P_CONFIG_VALUE, 'Custom configuration');
    END IF;
    
    COMMIT;
END;
/

-- Performance monitoring table
CREATE TABLE P_SCENARIO_PERFORMANCE_LOG (
    LOG_ID NUMBER PRIMARY KEY,
    SCENARIO_ID NUMBER NOT NULL,
    PROCESSING_TYPE VARCHAR2(20) NOT NULL, -- 'SEQUENTIAL' or 'PARALLEL'
    TOTAL_INSTANCES NUMBER,
    CHUNK_SIZE NUMBER,
    PARALLEL_DEGREE NUMBER,
    START_TIME TIMESTAMP,
    END_TIME TIMESTAMP,
    DURATION_SECONDS NUMBER,
    INSTANCES_PER_SECOND NUMBER,
    SUCCESS_COUNT NUMBER,
    FAILED_COUNT NUMBER,
    CREATED_DATE DATE DEFAULT SYSDATE
);

-- Create sequence for performance log
CREATE SEQUENCE SEQ_SCENARIO_PERF_LOG START WITH 1 INCREMENT BY 1;

-- Create procedure to log performance metrics
CREATE OR REPLACE PROCEDURE SP_LOG_SCENARIO_PERFORMANCE(
    P_SCENARIO_ID NUMBER,
    P_PROCESSING_TYPE VARCHAR2,
    P_TOTAL_INSTANCES NUMBER,
    P_CHUNK_SIZE NUMBER DEFAULT NULL,
    P_PARALLEL_DEGREE NUMBER DEFAULT NULL,
    P_START_TIME TIMESTAMP,
    P_END_TIME TIMESTAMP,
    P_SUCCESS_COUNT NUMBER DEFAULT NULL,
    P_FAILED_COUNT NUMBER DEFAULT NULL
)
IS
    V_DURATION_SECONDS NUMBER;
    V_INSTANCES_PER_SECOND NUMBER;
BEGIN
    -- Calculate duration in seconds
    V_DURATION_SECONDS := EXTRACT(DAY FROM (P_END_TIME - P_START_TIME)) * 86400 +
                         EXTRACT(HOUR FROM (P_END_TIME - P_START_TIME)) * 3600 +
                         EXTRACT(MINUTE FROM (P_END_TIME - P_START_TIME)) * 60 +
                         EXTRACT(SECOND FROM (P_END_TIME - P_START_TIME));
    
    -- Calculate instances per second
    IF V_DURATION_SECONDS > 0 THEN
        V_INSTANCES_PER_SECOND := P_TOTAL_INSTANCES / V_DURATION_SECONDS;
    ELSE
        V_INSTANCES_PER_SECOND := 0;
    END IF;
    
    INSERT INTO P_SCENARIO_PERFORMANCE_LOG (
        LOG_ID, SCENARIO_ID, PROCESSING_TYPE, TOTAL_INSTANCES, CHUNK_SIZE, PARALLEL_DEGREE,
        START_TIME, END_TIME, DURATION_SECONDS, INSTANCES_PER_SECOND, SUCCESS_COUNT, FAILED_COUNT
    ) VALUES (
        SEQ_SCENARIO_PERF_LOG.NEXTVAL, P_SCENARIO_ID, P_PROCESSING_TYPE, P_TOTAL_INSTANCES, 
        P_CHUNK_SIZE, P_PARALLEL_DEGREE, P_START_TIME, P_END_TIME, V_DURATION_SECONDS, 
        V_INSTANCES_PER_SECOND, P_SUCCESS_COUNT, P_FAILED_COUNT
    );
    
    COMMIT;
END;
/

-- Create view for performance analysis
CREATE OR REPLACE VIEW V_SCENARIO_PERFORMANCE_ANALYSIS AS
SELECT 
    SCENARIO_ID,
    PROCESSING_TYPE,
    AVG(INSTANCES_PER_SECOND) AS AVG_INSTANCES_PER_SECOND,
    MAX(INSTANCES_PER_SECOND) AS MAX_INSTANCES_PER_SECOND,
    MIN(INSTANCES_PER_SECOND) AS MIN_INSTANCES_PER_SECOND,
    AVG(TOTAL_INSTANCES) AS AVG_TOTAL_INSTANCES,
    AVG(DURATION_SECONDS) AS AVG_DURATION_SECONDS,
    COUNT(*) AS EXECUTION_COUNT,
    MAX(CREATED_DATE) AS LAST_EXECUTION
FROM P_SCENARIO_PERFORMANCE_LOG
GROUP BY SCENARIO_ID, PROCESSING_TYPE
ORDER BY SCENARIO_ID, PROCESSING_TYPE;

-- Grant necessary permissions (adjust schema names as needed)
-- GRANT SELECT, INSERT, UPDATE ON P_SCENARIO_PARALLEL_CONFIG TO <your_application_user>;
-- GRANT SELECT, INSERT ON P_SCENARIO_PERFORMANCE_LOG TO <your_application_user>;
-- GRANT SELECT ON V_SCENARIO_PERFORMANCE_ANALYSIS TO <your_application_user>;
