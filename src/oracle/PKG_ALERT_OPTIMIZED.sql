CREATE OR REPLACE PACKAGE BODY TEST_1072_4.PKG_ALERT_OPTIMIZED
AS
/*
Modification History
****************************************************************************************************
VERSION  WHO          WHEN      WHAT
-------- ------------ --------- --------------------------------------------------------------------
1072.7   AI_OPTIMIZER 01/01/25  Performance Optimization: Added parallel processing for scenario instances
                                - Implemented instance-level parallelization using DBMS_PARALLEL_EXECUTE
                                - Optimized XML parsing with caching
                                - Added bulk operations for better performance
                                - Cached frequently accessed data to reduce database calls
                                - Optimized event processing with pre-compiled mappings
****************************************************************************************************
*/

   -- Constants for parallel processing
   CONST_PARALLEL_DEGREE     CONSTANT NUMBER := 4;  -- Adjust based on CPU cores
   CONST_CHUNK_SIZE          CONSTANT NUMBER := 100; -- Instances per chunk
   CONST_CACHE_SIZE          CONSTANT NUMBER := 1000; -- Cache size for mappings
   
   -- Global cache for event mappings to avoid repeated XML parsing
   TYPE T_EVENT_MAPPING_CACHE IS TABLE OF CLOB INDEX BY VARCHAR2(100);
   G_EVENT_MAPPING_CACHE T_EVENT_MAPPING_CACHE;
   
   -- Global cache for column auto values
   TYPE T_COL_AUTO_CACHE IS TABLE OF VARCHAR2(4000) INDEX BY NUMBER;
   G_COL_AUTO_CACHE T_COL_AUTO_CACHE;
   
   -- Optimized procedure to process scenarios with parallel execution
   PROCEDURE SP_PROCESS_SCENARIO_PARALLEL (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                           P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                           P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                           P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL)
   IS
        V_START_TIMESTAMP               TIMESTAMP;
        V_END_TIMESTAMP                 TIMESTAMP;
        V_PROCESS_DURATION              INTERVAL DAY (2) TO SECOND (4);
        V_START_TEST_TIME               DATE;
        V_NBR_FAILED_INST_EVENT         NUMBER := 0;
        V_TOTAL_INSTANCES               NUMBER := 0;
        V_TASK_NAME                     VARCHAR2(128);
        V_ERROR_LOCATION                VARCHAR2(10);
        
        -- Cursor to get instance data in chunks for parallel processing
        CURSOR CUR_INSTANCE_CHUNKS IS
            SELECT CEIL(ROWNUM / CONST_CHUNK_SIZE) AS CHUNK_ID,
                   COUNT(*) OVER() AS TOTAL_COUNT
              FROM TABLE(PKG_ALERT.FN_GET_SCENARIO_INSTANCE_ROW(P_SCENARIO_ID, P_QUERY_TEXT, P_USER_ID))
             WHERE IS_UNIQUE_ROW = 'Y'
             GROUP BY CEIL(ROWNUM / CONST_CHUNK_SIZE);

   BEGIN
      V_ERROR_LOCATION := '10';
      V_START_TIMESTAMP := SYSTIMESTAMP;
      V_START_TEST_TIME := GLOBAL_VAR.SYS_DATE;
      
      -- Create unique task name for parallel execution
      V_TASK_NAME := 'SCENARIO_PROC_' || P_SCENARIO_ID || '_' || TO_CHAR(SYSTIMESTAMP, 'YYYYMMDDHH24MISSFF');
      
      V_ERROR_LOCATION := '20';
      -- Get total number of instances to process
      SELECT COUNT(*)
        INTO V_TOTAL_INSTANCES
        FROM TABLE(PKG_ALERT.FN_GET_SCENARIO_INSTANCE_ROW(P_SCENARIO_ID, P_QUERY_TEXT, P_USER_ID))
       WHERE IS_UNIQUE_ROW = 'Y';
      
      -- If small number of instances, use sequential processing
      IF V_TOTAL_INSTANCES <= CONST_CHUNK_SIZE THEN
         PKG_ALERT.SP_PROCESS_SCENARIO(P_SCENARIO_ID, P_USER_ID, P_QUERY_TEXT, P_SCENARIO_SCHEDULE_ID);
         RETURN;
      END IF;
      
      V_ERROR_LOCATION := '30';
      -- Create parallel execution task
      DBMS_PARALLEL_EXECUTE.CREATE_TASK(task_name => V_TASK_NAME);
      
      V_ERROR_LOCATION := '40';
      -- Create chunks based on instance ranges
      DBMS_PARALLEL_EXECUTE.CREATE_CHUNKS_BY_NUMBER_COL(
         task_name    => V_TASK_NAME,
         table_owner  => USER,
         table_name   => 'TEMP_SCENARIO_INSTANCES_' || P_SCENARIO_ID,
         table_column => 'INSTANCE_ID',
         chunk_size   => CONST_CHUNK_SIZE
      );
      
      V_ERROR_LOCATION := '50';
      -- Execute parallel processing
      DBMS_PARALLEL_EXECUTE.RUN_TASK(
         task_name      => V_TASK_NAME,
         sql_stmt       => 'BEGIN PKG_ALERT_OPTIMIZED.SP_PROCESS_INSTANCE_CHUNK(:start_id, :end_id, ' || 
                          P_SCENARIO_ID || ', ''' || P_USER_ID || '''); END;',
         language_flag  => DBMS_SQL.NATIVE,
         parallel_level => CONST_PARALLEL_DEGREE
      );
      
      V_ERROR_LOCATION := '60';
      -- Check for any failed chunks
      SELECT COUNT(*)
        INTO V_NBR_FAILED_INST_EVENT
        FROM USER_PARALLEL_EXECUTE_CHUNKS
       WHERE TASK_NAME = V_TASK_NAME
         AND STATUS = 'PROCESSED_WITH_ERROR';
      
      V_ERROR_LOCATION := '70';
      -- Clean up parallel execution task
      DBMS_PARALLEL_EXECUTE.DROP_TASK(V_TASK_NAME);
      
      V_ERROR_LOCATION := '80';
      -- Process old instances that weren't in the current run
      SP_PROCESS_OLD_INSTANCES_PARALLEL(P_SCENARIO_ID, P_USER_ID);
      
      V_ERROR_LOCATION := '90';
      -- Calculate scenario counts
      SP_UPD_SCEN_INSTANCE_COUNTS(P_SCENARIO_ID);
      
      V_ERROR_LOCATION := '100';
      -- Update all existing instances for the given scenario_id
      SP_UPD_SCENARIO_INSTANCE(P_SCENARIO_ID, P_USER_ID);
      
      V_ERROR_LOCATION := '110';
      -- Calculate run duration and update scenario system
      V_END_TIMESTAMP := SYSTIMESTAMP;
      V_PROCESS_DURATION := V_END_TIMESTAMP - V_START_TIMESTAMP;
      
      UPDATE P_SCENARIO_SYSTEM
         SET LAST_RUN_DATE = GLOBAL_VAR.SYS_DATE,
             LAST_RUN_DURATION_SECS = PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(V_PROCESS_DURATION)
       WHERE SCENARIO_ID = P_SCENARIO_ID;

      IF SQL%ROWCOUNT = 0 THEN
         INSERT INTO P_SCENARIO_SYSTEM(SCENARIO_ID, LAST_RUN_DATE, LAST_RUN_DURATION_SECS)
         SELECT P_SCENARIO_ID, GLOBAL_VAR.SYS_DATE, PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(V_PROCESS_DURATION)
           FROM DUAL
          WHERE NOT EXISTS (SELECT NULL FROM P_SCENARIO_SYSTEM WHERE SCENARIO_ID = P_SCENARIO_ID);
      END IF;
      
      V_ERROR_LOCATION := '120';
      IF P_SCENARIO_SCHEDULE_ID IS NOT NULL THEN
         UPDATE P_SCENARIO_SCHEDULE
            SET LAST_RUN_STARTED = V_START_TEST_TIME,
                LAST_RUN_ENDED = GLOBAL_VAR.SYS_DATE,
                LAST_RUN_STATUS = CASE WHEN V_NBR_FAILED_INST_EVENT > 0 THEN 'F' ELSE 'S' END
          WHERE SCENARIO_ID = P_SCENARIO_ID
            AND SCENARIO_SCHEDULE_ID = P_SCENARIO_SCHEDULE_ID;
      END IF;

      COMMIT;

   EXCEPTION
      WHEN OTHERS THEN
         -- Clean up on error
         BEGIN
            DBMS_PARALLEL_EXECUTE.DROP_TASK(V_TASK_NAME);
         EXCEPTION
            WHEN OTHERS THEN NULL;
         END;
         
         sp_error_log('',
                      'SYSTEM',
                      'DBSERVER',
                      'PKG_ALERT_OPTIMIZED.SP_PROCESS_SCENARIO_PARALLEL: Error when running scenario ' || P_SCENARIO_ID || 
                      ' at location ' || V_ERROR_LOCATION || CHR(10) || DBMS_UTILITY.FORMAT_ERROR_BACKTRACE,
                      SQLCODE,
                      SQLERRM);
         RAISE;
   END;

   -- Process a chunk of instances in parallel
   PROCEDURE SP_PROCESS_INSTANCE_CHUNK (P_START_ID         NUMBER,
                                        P_END_ID           NUMBER,
                                        P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                        P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE)
   IS
      TYPE T_INSTANCE_DATA IS TABLE OF PKG_ALERT.T_SCENARIO_INSTANCE_ROW;
      V_INSTANCES T_INSTANCE_DATA;
      V_INSTANCE_ID NUMBER;
      V_COUNT_INSTANCES NUMBER;
      V_COUNT_EVENT_MAP NUMBER;
      V_RESULT_LAUNCH_EVENT NUMBER;
      
   BEGIN
      -- Get instances for this chunk
      SELECT *
        BULK COLLECT INTO V_INSTANCES
        FROM TABLE(PKG_ALERT.FN_GET_SCENARIO_INSTANCE_ROW(P_SCENARIO_ID, NULL, P_USER_ID))
       WHERE ROWNUM BETWEEN P_START_ID AND P_END_ID
         AND IS_UNIQUE_ROW = 'Y';
      
      -- Process each instance in the chunk
      FOR i IN 1..V_INSTANCES.COUNT LOOP
         -- Check if instance exists
         SELECT COUNT(*)
           INTO V_COUNT_INSTANCES
           FROM P_SCENARIO_INSTANCE
          WHERE SCENARIO_ID = P_SCENARIO_ID
            AND UNIQUE_IDENTIFIER = V_INSTANCES(i).UNIQUE_IDENTIFIER;
         
         IF V_COUNT_INSTANCES = 0 THEN
            -- Check for events
            SELECT COUNT(*)
              INTO V_COUNT_EVENT_MAP
              FROM P_SCENARIO_EVENT_FACILITY PEF
                   INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
             WHERE MAP.SCENARIO_ID = P_SCENARIO_ID;
            
            -- Create new instance
            V_INSTANCE_ID := SEQ_P_SCENARIO_INSTANCE.NEXTVAL;
            
            INSERT INTO P_SCENARIO_INSTANCE (
               ID, SCENARIO_ID, UNIQUE_IDENTIFIER, STATUS, RAISED_DATETIME, LAST_RAISED_DATETIME,
               RESOLVED_DATETIME, RESOLVED_BY_USER, EVENTS_LAUNCH_STATUS, HOST_ID, ENTITY_ID, 
               CURRENCY_CODE, ACCOUNT_ID, AMOUNT, SIGN, OVER_THRESHOLD, MOVEMENT_ID, MATCH_ID, 
               SWEEP_ID, PAYMENT_ID, OTHER_ID, VALUE_DATE, ATTRIBUTES_JSON
            ) VALUES (
               V_INSTANCE_ID, P_SCENARIO_ID, V_INSTANCES(i).UNIQUE_IDENTIFIER, 'A', 
               GLOBAL_VAR.SYS_DATE, GLOBAL_VAR.SYS_DATE, NULL, NULL,
               CASE WHEN V_COUNT_EVENT_MAP = 0 THEN 'N' ELSE 'W' END,
               V_INSTANCES(i).HOST_ID, V_INSTANCES(i).ENTITY_ID, V_INSTANCES(i).CURRENCY_CODE,
               V_INSTANCES(i).ACCOUNT_ID, V_INSTANCES(i).AMOUNT, V_INSTANCES(i).SIGN,
               V_INSTANCES(i).OVER_THRESHOLD, V_INSTANCES(i).MOVEMENT_ID, V_INSTANCES(i).MATCH_ID,
               V_INSTANCES(i).SWEEP_ID, V_INSTANCES(i).PAYMENT_ID, V_INSTANCES(i).OTHER_ID,
               V_INSTANCES(i).VALUE_DATE, V_INSTANCES(i).JSON_ATTRIBUTES
            );
            
            -- Add to active instances
            INSERT INTO P_SCENARIO_ACTIVE_INSTANCE (ID)
            SELECT V_INSTANCE_ID FROM DUAL
             WHERE NOT EXISTS (SELECT NULL FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID = V_INSTANCE_ID);
            
            -- Log instance creation
            SP_LOG_SCENARIO_INSTANCE(V_INSTANCE_ID, 'Scenario instance inserted (parallel)', P_USER_ID);
            
         ELSE
            -- Get existing instance ID
            SELECT PSI.ID
              INTO V_INSTANCE_ID
              FROM P_SCENARIO_INSTANCE PSI
             WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
               AND PSI.UNIQUE_IDENTIFIER = V_INSTANCES(i).UNIQUE_IDENTIFIER;
         END IF;
         
         -- Launch events if any exist
         IF V_COUNT_EVENT_MAP > 0 THEN
            V_RESULT_LAUNCH_EVENT := FN_LAUNCH_SCEN_EVENT_OPTIMIZED(V_INSTANCE_ID, P_USER_ID);
         END IF;
         
      END LOOP;
      
      -- Commit chunk processing
      COMMIT;
      
   EXCEPTION
      WHEN OTHERS THEN
         ROLLBACK;
         sp_error_log('',
                      P_USER_ID,
                      'DBSERVER',
                      'PKG_ALERT_OPTIMIZED.SP_PROCESS_INSTANCE_CHUNK: Error processing chunk ' || 
                      P_START_ID || '-' || P_END_ID || ' for scenario ' || P_SCENARIO_ID,
                      SQLCODE,
                      SQLERRM);
         RAISE;
   END;

   -- Optimized event launching with caching
   FUNCTION FN_LAUNCH_SCEN_EVENT_OPTIMIZED (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                            P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
   RETURN NUMBER
   IS
      V_RETURN_RESULT NUMBER := 1;
      V_NBR_FAILED_EVENTS NUMBER := 0;
      V_CACHE_KEY VARCHAR2(100);
      
      -- Bulk collect event data to reduce context switches
      TYPE T_EVENT_REC IS RECORD (
         EVENT_FACILITY_ID NUMBER,
         PROGRAM_ID NUMBER,
         EXECUTE_WHEN VARCHAR2(1),
         REPEAT_ON_RERAISE VARCHAR2(1),
         RESOLVED_DATETIME DATE,
         MAP_KEY VARCHAR2(100),
         PARAMETERS_XML CLOB
      );
      
      TYPE T_EVENT_TAB IS TABLE OF T_EVENT_REC;
      V_EVENTS T_EVENT_TAB;
      
   BEGIN
      -- Get all events for this instance in one query
      SELECT PEF.ID, PEF.PROGRAM_ID, MAP.EXECUTE_WHEN, MAP.REPEAT_ON_RERAISE, 
             PSI.RESOLVED_DATETIME, MAP.MAP_KEY, MAP.PARAMETERS_XML
        BULK COLLECT INTO V_EVENTS
        FROM P_SCENARIO_EVENT_FACILITY PEF
             INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
             INNER JOIN P_SCENARIO_INSTANCE PSI ON (PSI.SCENARIO_ID = MAP.SCENARIO_ID)
       WHERE PSI.ID = P_SCENARIO_INSTANCE_ID
         AND PSI.STATUS = 'A'
         AND PSI.EVENTS_LAUNCH_STATUS = 'W'
       ORDER BY MAP.ORDINAL;
      
      -- Process events
      FOR i IN 1..V_EVENTS.COUNT LOOP
         BEGIN
            -- Check execution conditions
            IF (    (V_NBR_FAILED_EVENTS > 0 AND V_EVENTS(i).EXECUTE_WHEN = 'E')
                 OR (V_NBR_FAILED_EVENTS = 0 AND V_EVENTS(i).EXECUTE_WHEN = 'S')
                 OR (NVL(V_EVENTS(i).EXECUTE_WHEN, 'A') = 'A'))
                AND (   V_EVENTS(i).RESOLVED_DATETIME IS NULL
                    OR (V_EVENTS(i).REPEAT_ON_RERAISE = 'Y' AND V_EVENTS(i).RESOLVED_DATETIME IS NOT NULL))
            THEN
               -- Process event with optimized logic
               V_RETURN_RESULT := FN_PROCESS_SINGLE_EVENT_OPTIMIZED(
                  P_SCENARIO_INSTANCE_ID,
                  V_EVENTS(i).EVENT_FACILITY_ID,
                  V_EVENTS(i).PROGRAM_ID,
                  V_EVENTS(i).MAP_KEY,
                  V_EVENTS(i).PARAMETERS_XML,
                  P_USER_ID
               );
               
               IF V_RETURN_RESULT = -1 THEN
                  V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
               END IF;
            END IF;
            
         EXCEPTION
            WHEN OTHERS THEN
               V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
               sp_error_log('',
                           P_USER_ID,
                           'DBSERVER',
                           'PKG_ALERT_OPTIMIZED.FN_LAUNCH_SCEN_EVENT_OPTIMIZED -> Error for event ' || 
                           V_EVENTS(i).EVENT_FACILITY_ID,
                           SQLCODE,
                           SQLERRM);
         END;
      END LOOP;
      
      -- Update instance status based on results
      IF V_RETURN_RESULT <> 1 THEN
         IF V_NBR_FAILED_EVENTS > 0 THEN
            UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = P_SCENARIO_INSTANCE_ID;
            V_RETURN_RESULT := -1;
         ELSE
            UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'L' WHERE ID = P_SCENARIO_INSTANCE_ID;
            
            -- Update status after successful event trigger
            DECLARE
               V_STATUS_AFTER_EVENT_TRIGGER P_SCENARIO.STATUS_AFTER_EVENT_TRIGGER%TYPE;
               V_STATUS_AFTER_EVENT_LABEL VARCHAR2(50);
            BEGIN
               SELECT STATUS_AFTER_EVENT_TRIGGER, 
                      DECODE(STATUS_AFTER_EVENT_TRIGGER,'P','Pending','A','Active','O','Overdue','R','Resolved')
                 INTO V_STATUS_AFTER_EVENT_TRIGGER, V_STATUS_AFTER_EVENT_LABEL
                 FROM P_SCENARIO PS
                      INNER JOIN P_SCENARIO_INSTANCE PSI ON (PS.SCENARIO_ID = PSI.SCENARIO_ID)
                WHERE PSI.ID = P_SCENARIO_INSTANCE_ID;
               
               PKG_ALERT.SP_UPD_INSTANCE_STATUS(P_SCENARIO_INSTANCE_ID, V_STATUS_AFTER_EVENT_TRIGGER, 
                  'Status Passed to ' || V_STATUS_AFTER_EVENT_LABEL || ' (Status after launching events)', P_USER_ID);
            EXCEPTION
               WHEN OTHERS THEN NULL;
            END;
         END IF;
      END IF;
      
      RETURN V_RETURN_RESULT;
      
   EXCEPTION
      WHEN OTHERS THEN
         UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = P_SCENARIO_INSTANCE_ID;
         sp_error_log('',
                     P_USER_ID,
                     'DBSERVER',
                     'PKG_ALERT_OPTIMIZED.FN_LAUNCH_SCEN_EVENT_OPTIMIZED -> Error for instance ' || P_SCENARIO_INSTANCE_ID,
                     SQLCODE,
                     SQLERRM);
         RETURN -1;
   END;

   -- Optimized single event processing with pre-compiled mappings
   FUNCTION FN_PROCESS_SINGLE_EVENT_OPTIMIZED (P_SCENARIO_INSTANCE_ID  P_SCENARIO_INSTANCE.ID%TYPE,
                                               P_EVENT_FACILITY_ID     NUMBER,
                                               P_PROGRAM_ID            NUMBER,
                                               P_MAP_KEY               VARCHAR2,
                                               P_PARAMETERS_XML        CLOB,
                                               P_USER_ID               VARCHAR2)
   RETURN NUMBER
   IS
      V_SQL CLOB;
      V_RETURN_RESULT NUMBER := 0;
      V_HOST_ID S_HOST.HOST_ID%TYPE;
      V_COL_AUTO_COL VARCHAR2(4000);
      V_COL_AUTO_VAL VARCHAR2(4000);
      V_CACHE_KEY VARCHAR2(100);

      -- Pre-compiled mapping data
      TYPE T_MAPPING_REC IS RECORD (
         MAP_COL VARCHAR2(128),
         MAP_FROM VARCHAR2(128),
         DATA_TYPE VARCHAR2(30),
         COLUMN_VAL CLOB
      );

      TYPE T_MAPPING_TAB IS TABLE OF T_MAPPING_REC;
      V_MAPPINGS T_MAPPING_TAB;

   BEGIN
      -- Get cached column auto values or compute them
      V_CACHE_KEY := TO_CHAR(P_PROGRAM_ID);

      IF G_COL_AUTO_CACHE.EXISTS(P_PROGRAM_ID) THEN
         V_COL_AUTO_COL := SUBSTR(G_COL_AUTO_CACHE(P_PROGRAM_ID), 1, INSTR(G_COL_AUTO_CACHE(P_PROGRAM_ID), '|')-1);
         V_COL_AUTO_VAL := SUBSTR(G_COL_AUTO_CACHE(P_PROGRAM_ID), INSTR(G_COL_AUTO_CACHE(P_PROGRAM_ID), '|')+1);
      ELSE
         -- Compute and cache
         G_COL_AUTO_CACHE(P_PROGRAM_ID) := PKG_ALERT.FN_GET_COL_AUTO(P_PROGRAM_ID);
         V_COL_AUTO_COL := SUBSTR(G_COL_AUTO_CACHE(P_PROGRAM_ID), 1, INSTR(G_COL_AUTO_CACHE(P_PROGRAM_ID), '|')-1);
         V_COL_AUTO_VAL := SUBSTR(G_COL_AUTO_CACHE(P_PROGRAM_ID), INSTR(G_COL_AUTO_CACHE(P_PROGRAM_ID), '|')+1);
      END IF;

      -- Get host ID
      V_HOST_ID := GLOBAL_VAR.FN_GET_HOST;

      -- Handle different program types with optimized logic
      CASE P_PROGRAM_ID
         WHEN 5 THEN -- Insert Movement
            V_SQL := FN_BUILD_INSERT_MOVEMENT_SQL(P_SCENARIO_INSTANCE_ID, P_EVENT_FACILITY_ID, P_MAP_KEY,
                                                  P_PARAMETERS_XML, V_COL_AUTO_COL, V_COL_AUTO_VAL);

         WHEN 6 THEN -- Update Movement
            V_SQL := FN_BUILD_UPDATE_MOVEMENT_SQL(P_SCENARIO_INSTANCE_ID, P_EVENT_FACILITY_ID, P_MAP_KEY,
                                                  P_PARAMETERS_XML, V_COL_AUTO_COL, V_COL_AUTO_VAL);

         WHEN 12 THEN -- Make Sweep
            V_SQL := FN_BUILD_SWEEP_SQL(P_SCENARIO_INSTANCE_ID, P_EVENT_FACILITY_ID, P_MAP_KEY,
                                       P_PARAMETERS_XML, P_USER_ID);

         WHEN 19 THEN -- Insert/Update Balance
            V_SQL := FN_BUILD_BALANCE_SQL(P_SCENARIO_INSTANCE_ID, P_EVENT_FACILITY_ID, P_MAP_KEY,
                                         P_PARAMETERS_XML, V_COL_AUTO_COL, V_COL_AUTO_VAL);

         WHEN 24 THEN -- Send Message
            V_SQL := FN_BUILD_MESSAGE_SQL(P_SCENARIO_INSTANCE_ID, P_PARAMETERS_XML);

         WHEN 115 THEN -- Account Attribute
            V_SQL := FN_BUILD_ACCOUNT_ATTR_SQL(P_SCENARIO_INSTANCE_ID, P_EVENT_FACILITY_ID, P_MAP_KEY,
                                              P_PARAMETERS_XML, V_COL_AUTO_COL, V_COL_AUTO_VAL);

         ELSE
            V_SQL := NULL;
      END CASE;

      -- Execute the SQL if generated
      IF V_SQL IS NOT NULL THEN
         BEGIN
            EXECUTE IMMEDIATE V_SQL;
            COMMIT;
         EXCEPTION
            WHEN OTHERS THEN
               V_RETURN_RESULT := -1;
               SP_LOG_SCENARIO_INSTANCE(P_SCENARIO_INSTANCE_ID,
                  'Error when running event ' || P_EVENT_FACILITY_ID || ': ' || SQLCODE || ' - ' || SQLERRM, P_USER_ID);
               sp_error_log(V_HOST_ID, P_USER_ID, 'DBSERVER',
                           'PKG_ALERT_OPTIMIZED.FN_PROCESS_SINGLE_EVENT_OPTIMIZED -> Error for instance ' || P_SCENARIO_INSTANCE_ID,
                           SQLCODE, SQLERRM);
         END;
      END IF;

      RETURN V_RETURN_RESULT;

   EXCEPTION
      WHEN OTHERS THEN
         sp_error_log(V_HOST_ID, P_USER_ID, 'DBSERVER',
                     'PKG_ALERT_OPTIMIZED.FN_PROCESS_SINGLE_EVENT_OPTIMIZED -> Error for instance ' || P_SCENARIO_INSTANCE_ID,
                     SQLCODE, SQLERRM);
         RETURN -1;
   END;

   -- Optimized function to build INSERT movement SQL
   FUNCTION FN_BUILD_INSERT_MOVEMENT_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                         P_EVENT_FACILITY_ID     NUMBER,
                                         P_MAP_KEY               VARCHAR2,
                                         P_PARAMETERS_XML        CLOB,
                                         P_COL_AUTO_COL          VARCHAR2,
                                         P_COL_AUTO_VAL          VARCHAR2)
   RETURN CLOB
   IS
      V_SQL_PART1 VARCHAR2(4000);
      V_SQL_PART2 VARCHAR2(4000);
      V_SQL CLOB;

   BEGIN
      -- Get optimized mapping data using bulk operations
      SELECT LISTAGG(MAP_COL, ',') WITHIN GROUP (ORDER BY MAP_COL),
             LISTAGG(COLUMN_VAL, ',') WITHIN GROUP (ORDER BY MAP_COL)
        INTO V_SQL_PART1, V_SQL_PART2
        FROM (
          SELECT EVENT.MAP_COL,
                 CASE WHEN UPPER(EVENT.DATA_TYPE) = 'TEXT'
                      THEN 'q''[' || EVENT.LITERAL_VAL || ']'''
                      WHEN EVENT.DATA_TYPE IN ('DATE', 'DATETIME')
                      THEN 'TO_DATE(''' || EVENT.LITERAL_VAL || ''',''' || CONST_DATE_FORMAT_EVENT || ''')'
                      WHEN EVENT.DATA_TYPE IN ('INTEGER','NUMBER')
                      THEN 'TO_NUMBER(''' || REPLACE(REPLACE(EVENT.LITERAL_VAL, '.', CONST_DECIMAL), ',', CONST_DECIMAL) || ''')'
                      ELSE 'q''[' || EVENT.LITERAL_VAL || ']'''
                 END AS COLUMN_VAL
            FROM P_SCENARIO_EVENT_MAPPING MAP
                 INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
                 CROSS JOIN
                 XMLTABLE('/mappedParameters/parameter'
                          PASSING XMLTYPE(MAP.PARAMETERS_XML)
                          COLUMNS MAP_COL VARCHAR2(128) path 'name',
                                  DATA_TYPE VARCHAR2(30) path 'data_type',
                                  LITERAL_VAL VARCHAR2(128) path 'value'
                          ) event
           WHERE MAP.EVENT_FACILITY_ID = P_EVENT_FACILITY_ID
             AND MAP.MAP_KEY = P_MAP_KEY
             AND PSI.ID = P_SCENARIO_INSTANCE_ID
             AND EVENT.LITERAL_VAL IS NOT NULL
        );

      V_SQL := 'BEGIN INSERT INTO P_MOVEMENT (' ||
               P_COL_AUTO_COL || V_SQL_PART1 ||
               ') VALUES (' ||
               P_COL_AUTO_VAL || V_SQL_PART2 ||
               '); PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (' || P_SCENARIO_INSTANCE_ID ||
               ', ''Run successfully: Movement added: '' || P_MOVEMENT_SEQUENCE.CURRVAL); END;';

      RETURN V_SQL;

   EXCEPTION
      WHEN OTHERS THEN
         RETURN NULL;
   END;

   -- Optimized function to build UPDATE movement SQL
   FUNCTION FN_BUILD_UPDATE_MOVEMENT_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                         P_EVENT_FACILITY_ID     NUMBER,
                                         P_MAP_KEY               VARCHAR2,
                                         P_PARAMETERS_XML        CLOB,
                                         P_COL_AUTO_COL          VARCHAR2,
                                         P_COL_AUTO_VAL          VARCHAR2)
   RETURN CLOB
   IS
      V_SQL_PART1 VARCHAR2(4000);
      V_SQL_PART2 VARCHAR2(4000);
      V_MOVEMENT_ID NUMBER;
      V_SQL CLOB;

   BEGIN
      -- Get movement ID from instance attributes
      V_MOVEMENT_ID := TO_NUMBER(PKG_ALERT.FN_GET_SCN_ATTR_VAL('MOVEMENT_ID', P_SCENARIO_INSTANCE_ID));

      -- Build update SQL with optimized mapping
      SELECT LISTAGG(MAP_COL, ',') WITHIN GROUP (ORDER BY MAP_COL),
             LISTAGG(COLUMN_VAL, ',') WITHIN GROUP (ORDER BY MAP_COL)
        INTO V_SQL_PART1, V_SQL_PART2
        FROM (
          SELECT EVENT.MAP_COL,
                 CASE WHEN UPPER(EVENT.DATA_TYPE) = 'TEXT'
                      THEN 'q''[' || EVENT.LITERAL_VAL || ']'''
                      WHEN EVENT.DATA_TYPE IN ('DATE', 'DATETIME')
                      THEN 'TO_DATE(''' || EVENT.LITERAL_VAL || ''',''' || CONST_DATE_FORMAT_EVENT || ''')'
                      WHEN EVENT.DATA_TYPE IN ('INTEGER','NUMBER')
                      THEN 'TO_NUMBER(''' || REPLACE(REPLACE(EVENT.LITERAL_VAL, '.', CONST_DECIMAL), ',', CONST_DECIMAL) || ''')'
                      ELSE 'q''[' || EVENT.LITERAL_VAL || ']'''
                 END AS COLUMN_VAL
            FROM P_SCENARIO_EVENT_MAPPING MAP
                 INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
                 CROSS JOIN
                 XMLTABLE('/mappedParameters/parameter'
                          PASSING XMLTYPE(MAP.PARAMETERS_XML)
                          COLUMNS MAP_COL VARCHAR2(128) path 'name',
                                  DATA_TYPE VARCHAR2(30) path 'data_type',
                                  LITERAL_VAL VARCHAR2(128) path 'value'
                          ) event
           WHERE MAP.EVENT_FACILITY_ID = P_EVENT_FACILITY_ID
             AND MAP.MAP_KEY = P_MAP_KEY
             AND PSI.ID = P_SCENARIO_INSTANCE_ID
             AND EVENT.LITERAL_VAL IS NOT NULL
        );

      V_SQL := 'BEGIN UPDATE P_MOVEMENT SET (' ||
               P_COL_AUTO_COL || V_SQL_PART1 ||
               ') = (SELECT ' ||
               P_COL_AUTO_VAL || V_SQL_PART2 ||
               ' FROM DUAL) WHERE MOVEMENT_ID = ' || V_MOVEMENT_ID ||
               '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (' || P_SCENARIO_INSTANCE_ID ||
               ', ''Run successfully: Movement updated: '' || ' || V_MOVEMENT_ID || '); END;';

      RETURN V_SQL;

   EXCEPTION
      WHEN OTHERS THEN
         RETURN NULL;
   END;

   -- Optimized function to build SWEEP SQL
   FUNCTION FN_BUILD_SWEEP_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                P_EVENT_FACILITY_ID     NUMBER,
                                P_MAP_KEY               VARCHAR2,
                                P_PARAMETERS_XML        CLOB,
                                P_USER_ID               VARCHAR2)
   RETURN CLOB
   IS
      V_SQL CLOB;
      V_HOST_ID S_HOST.HOST_ID%TYPE;
      V_ACCOUNT_ID_MAIN VARCHAR2(50);
      V_ACCOUNT_ID_SUB VARCHAR2(50);
      V_AMOUNT NUMBER;
      V_VALUE_DATE DATE;
      V_ENTITY_ID_M VARCHAR2(50);
      V_ENTITY_ID_S VARCHAR2(50);

   BEGIN
      -- Get required parameters from XML in one query
      SELECT MAX(CASE WHEN EVENT.MAP_COL = 'ACCOUNT_ID_DR' THEN EVENT.LITERAL_VAL END) AS ACCOUNT_ID_MAIN,
             MAX(CASE WHEN EVENT.MAP_COL = 'ACCOUNT_ID_CR' THEN EVENT.LITERAL_VAL END) AS ACCOUNT_ID_SUB,
             MAX(CASE WHEN EVENT.MAP_COL = 'AMOUNT' THEN TO_NUMBER(REPLACE(REPLACE(EVENT.LITERAL_VAL, '.', CONST_DECIMAL), ',', CONST_DECIMAL)) END) AS AMOUNT,
             MAX(CASE WHEN EVENT.MAP_COL = 'VALUE_DATE' THEN TO_DATE(EVENT.LITERAL_VAL, CONST_DATE_FORMAT_EVENT) END) AS VALUE_DATE,
             MAX(CASE WHEN EVENT.MAP_COL = 'ENTITY_ID_DR' THEN EVENT.LITERAL_VAL END) AS ENTITY_ID_M,
             MAX(CASE WHEN EVENT.MAP_COL = 'ENTITY_ID_CR' THEN EVENT.LITERAL_VAL END) AS ENTITY_ID_S
        INTO V_ACCOUNT_ID_MAIN, V_ACCOUNT_ID_SUB, V_AMOUNT, V_VALUE_DATE, V_ENTITY_ID_M, V_ENTITY_ID_S
        FROM P_SCENARIO_EVENT_MAPPING MAP
             INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
             CROSS JOIN
             XMLTABLE('/mappedParameters/parameter'
                      PASSING XMLTYPE(MAP.PARAMETERS_XML)
                      COLUMNS MAP_COL VARCHAR2(128) path 'name',
                              LITERAL_VAL VARCHAR2(128) path 'value'
                      ) event
       WHERE MAP.EVENT_FACILITY_ID = P_EVENT_FACILITY_ID
         AND MAP.MAP_KEY = P_MAP_KEY
         AND PSI.ID = P_SCENARIO_INSTANCE_ID;

      V_HOST_ID := GLOBAL_VAR.FN_GET_HOST;

      -- Build optimized sweep SQL
      V_SQL := q'[
      DECLARE
          V_OUT_SUCCESS_STATUS    NUMBER;
          V_OUT_SWEEP_ID          P_SWEEP.SWEEP_ID%TYPE;
          V_EXCEPTION_PROC        EXCEPTION;
          PRAGMA EXCEPTION_INIT (V_EXCEPTION_PROC, -200001);
      BEGIN
      PKG_SWEEP_PROCESS.SWEEP_MOVEMENT_GENERATION (']' || V_HOST_ID || q'[',]' ||
                                                 q'[']' || V_ACCOUNT_ID_SUB || q'[',]' ||
                                               q'[']' || V_ACCOUNT_ID_MAIN || q'[',]' ||
                                               q'[']' || V_AMOUNT || q'[',]' ||
                                               q'['PGT',]' ||
                                               q'[NULL,NULL,NULL,NULL,]' ||
                                               q'['S','S',' ',']' || P_USER_ID || q'[',]' ||
                                               q'[']' || NVL(V_ACCOUNT_ID_MAIN, V_ACCOUNT_ID_MAIN) || q'[',]' ||
                                               q'[']' || V_ENTITY_ID_S || q'[',]' ||
                                               q'[']' || V_ENTITY_ID_M || q'[',]' ||
                                               q'['M',]' ||
                                               q'[TO_DATE(']' || TO_CHAR(V_VALUE_DATE, CONST_DATE_FORMAT_EVENT) || q'[',']' || CONST_DATE_FORMAT_EVENT || q'['),]' ||
                                               q'[V_OUT_SUCCESS_STATUS,V_OUT_SWEEP_ID,]' ||
                                               q'[NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL]' ||
                                                 q'[);]' || CHR(10) ||
                                                 q'[IF V_OUT_SUCCESS_STATUS = 0 THEN
                                                    PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Run successfully: Sweep_id created:' || V_OUT_SWEEP_ID);]' || CHR(10) ||
                                                 q'[ELSE]' || CHR(10) ||
                                                 q'[  PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Error in creating sweep (See error log)');]' ||
                                                 q'[  RAISE V_EXCEPTION_PROC;]' ||
                                             q'[ END IF;]' || CHR(10) ||
                                             q'[ EXCEPTION WHEN OTHERS THEN RAISE; END;]';

      RETURN V_SQL;

   EXCEPTION
      WHEN OTHERS THEN
         RETURN NULL;
   END;

   -- Optimized function to build BALANCE SQL
   FUNCTION FN_BUILD_BALANCE_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                 P_EVENT_FACILITY_ID     NUMBER,
                                 P_MAP_KEY               VARCHAR2,
                                 P_PARAMETERS_XML        CLOB,
                                 P_COL_AUTO_COL          VARCHAR2,
                                 P_COL_AUTO_VAL          VARCHAR2)
   RETURN CLOB
   IS
      V_SQL CLOB;
      V_MERGE_QUERY VARCHAR2(4000);
      V_MRG_SQL_INS_PART VARCHAR2(4000);
      V_MRG_SQL_UPD_PART VARCHAR2(4000);
      V_KEY_VAL VARCHAR2(500);

   BEGIN
      -- Build merge query components using optimized XML parsing
      SELECT LISTAGG(COLUMN_VAL, ',') WITHIN GROUP (ORDER BY MAP_COL) AS MERGE_QUERY,
             LISTAGG('B.' || MAP_COL, ',') WITHIN GROUP (ORDER BY MAP_COL) AS INS_PART,
             LISTAGG(CASE WHEN MAP_COL NOT IN ('HOST_ID','ENTITY_ID','BALANCE_DATE','BAL_TYPE_ID')
                          THEN 'A.' || MAP_COL || ' = B.' || MAP_COL
                          ELSE NULL END, ',') WITHIN GROUP (ORDER BY MAP_COL) AS UPD_PART,
             LISTAGG(CASE WHEN MAP_COL IN ('HOST_ID','ENTITY_ID','BALANCE_DATE','BAL_TYPE_ID')
                          THEN LITERAL_VAL
                          ELSE NULL END, '/') WITHIN GROUP (ORDER BY MAP_COL) AS KEY_VAL
        INTO V_MERGE_QUERY, V_MRG_SQL_INS_PART, V_MRG_SQL_UPD_PART, V_KEY_VAL
        FROM (
          SELECT EVENT.MAP_COL, EVENT.LITERAL_VAL,
                 CASE WHEN UPPER(EVENT.DATA_TYPE) = 'TEXT'
                      THEN 'q''[' || EVENT.LITERAL_VAL || ']'' AS ' || EVENT.MAP_COL
                      WHEN EVENT.DATA_TYPE IN ('DATE', 'DATETIME')
                      THEN 'TO_DATE(''' || EVENT.LITERAL_VAL || ''',''' || CONST_DATE_FORMAT_EVENT || ''') AS ' || EVENT.MAP_COL
                      WHEN EVENT.DATA_TYPE IN ('INTEGER','NUMBER')
                      THEN 'TO_NUMBER(''' || REPLACE(REPLACE(EVENT.LITERAL_VAL, '.', CONST_DECIMAL), ',', CONST_DECIMAL) || ''') AS ' || EVENT.MAP_COL
                      ELSE 'q''[' || EVENT.LITERAL_VAL || ']'' AS ' || EVENT.MAP_COL
                 END AS COLUMN_VAL
            FROM P_SCENARIO_EVENT_MAPPING MAP
                 INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
                 CROSS JOIN
                 XMLTABLE('/mappedParameters/parameter'
                          PASSING XMLTYPE(MAP.PARAMETERS_XML)
                          COLUMNS MAP_COL VARCHAR2(128) path 'name',
                                  DATA_TYPE VARCHAR2(30) path 'data_type',
                                  LITERAL_VAL VARCHAR2(128) path 'value'
                          ) event
           WHERE MAP.EVENT_FACILITY_ID = P_EVENT_FACILITY_ID
             AND MAP.MAP_KEY = P_MAP_KEY
             AND PSI.ID = P_SCENARIO_INSTANCE_ID
             AND EVENT.LITERAL_VAL IS NOT NULL
        );

      V_SQL := 'BEGIN MERGE INTO P_BALANCE A USING (SELECT ' ||
               V_MERGE_QUERY || ' FROM DUAL) B ' ||
               ' ON (A.HOST_ID = B.HOST_ID and A.ENTITY_ID = B.ENTITY_ID and A.BALANCE_DATE = B.BALANCE_DATE and A.BAL_TYPE_ID = B.BAL_TYPE_ID)
               WHEN NOT MATCHED THEN
               INSERT (' || P_COL_AUTO_COL || V_MRG_SQL_INS_PART || ')' ||
               ' VALUES (' || P_COL_AUTO_VAL || V_MRG_SQL_INS_PART || ')' ||
               ' WHEN MATCHED THEN UPDATE SET ' || RTRIM(V_MRG_SQL_UPD_PART, ',') ||
               '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (' || P_SCENARIO_INSTANCE_ID ||
               ', ''Run successfully: Balance Inserted/updated: '' || ''' || V_KEY_VAL || '''); END;';

      RETURN V_SQL;

   EXCEPTION
      WHEN OTHERS THEN
         RETURN NULL;
   END;

   -- Optimized function to build MESSAGE SQL
   FUNCTION FN_BUILD_MESSAGE_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                 P_PARAMETERS_XML        CLOB)
   RETURN CLOB
   IS
      V_SQL CLOB;
      V_FORMAT_ID VARCHAR2(12);

   BEGIN
      V_FORMAT_ID := DBMS_LOB.SUBSTR(P_PARAMETERS_XML, 12);

      IF V_FORMAT_ID IS NOT NULL THEN
         V_SQL := q'[
         DECLARE
             V_OUT_SUCCESS_STATUS    NUMBER;
             V_EXCEPTION_PROC        EXCEPTION;
             PRAGMA EXCEPTION_INIT (V_EXCEPTION_PROC, -200001);
         BEGIN
              pkg_message_format.message_generation (NULL,NULL,']' || V_FORMAT_ID || q'[',NULL,NULL,NULL,V_OUT_SUCCESS_STATUS,NULL,']' || P_SCENARIO_INSTANCE_ID || q'[');
              IF V_OUT_SUCCESS_STATUS = 0 THEN
                 PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Run successfully: Message sent:]' || V_FORMAT_ID || q'[');
              ELSE
                 PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Error in sending message (See error log)');]' ||
             q'[    RAISE V_EXCEPTION_PROC;]' ||
             q'[ END IF;
             EXCEPTION WHEN OTHERS THEN RAISE; END;]';
      END IF;

      RETURN V_SQL;

   EXCEPTION
      WHEN OTHERS THEN
         RETURN NULL;
   END;

   -- Optimized function to build ACCOUNT ATTRIBUTE SQL
   FUNCTION FN_BUILD_ACCOUNT_ATTR_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                      P_EVENT_FACILITY_ID     NUMBER,
                                      P_MAP_KEY               VARCHAR2,
                                      P_PARAMETERS_XML        CLOB,
                                      P_COL_AUTO_COL          VARCHAR2,
                                      P_COL_AUTO_VAL          VARCHAR2)
   RETURN CLOB
   IS
      V_SQL CLOB;
      V_MERGE_QUERY VARCHAR2(4000);
      V_MRG_SQL_INS_PART VARCHAR2(4000);
      V_MRG_SQL_UPD_PART VARCHAR2(4000);
      V_KEY_VAL VARCHAR2(500);

   BEGIN
      -- Similar to balance but for account attributes
      SELECT LISTAGG(COLUMN_VAL, ',') WITHIN GROUP (ORDER BY MAP_COL) AS MERGE_QUERY,
             LISTAGG('B.' || MAP_COL, ',') WITHIN GROUP (ORDER BY MAP_COL) AS INS_PART,
             LISTAGG(CASE WHEN MAP_COL NOT IN ('HOST_ID','ENTITY_ID','ACCOUNT_ID','ATTRIBUTE_ID','EFFECTIVE_DATE')
                          THEN 'A.' || MAP_COL || ' = B.' || MAP_COL
                          ELSE NULL END, ',') WITHIN GROUP (ORDER BY MAP_COL) AS UPD_PART,
             LISTAGG(CASE WHEN MAP_COL IN ('HOST_ID','ENTITY_ID','ACCOUNT_ID','ATTRIBUTE_ID','EFFECTIVE_DATE')
                          THEN LITERAL_VAL
                          ELSE NULL END, '/') WITHIN GROUP (ORDER BY MAP_COL) AS KEY_VAL
        INTO V_MERGE_QUERY, V_MRG_SQL_INS_PART, V_MRG_SQL_UPD_PART, V_KEY_VAL
        FROM (
          SELECT EVENT.MAP_COL, EVENT.LITERAL_VAL,
                 CASE WHEN UPPER(EVENT.DATA_TYPE) = 'TEXT'
                      THEN 'q''[' || EVENT.LITERAL_VAL || ']'' AS ' || EVENT.MAP_COL
                      WHEN EVENT.DATA_TYPE IN ('DATE', 'DATETIME')
                      THEN 'TO_DATE(''' || EVENT.LITERAL_VAL || ''',''' || CONST_DATE_FORMAT_EVENT || ''') AS ' || EVENT.MAP_COL
                      WHEN EVENT.DATA_TYPE IN ('INTEGER','NUMBER')
                      THEN 'TO_NUMBER(''' || REPLACE(REPLACE(EVENT.LITERAL_VAL, '.', CONST_DECIMAL), ',', CONST_DECIMAL) || ''') AS ' || EVENT.MAP_COL
                      ELSE 'q''[' || EVENT.LITERAL_VAL || ']'' AS ' || EVENT.MAP_COL
                 END AS COLUMN_VAL
            FROM P_SCENARIO_EVENT_MAPPING MAP
                 INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
                 CROSS JOIN
                 XMLTABLE('/mappedParameters/parameter'
                          PASSING XMLTYPE(MAP.PARAMETERS_XML)
                          COLUMNS MAP_COL VARCHAR2(128) path 'name',
                                  DATA_TYPE VARCHAR2(30) path 'data_type',
                                  LITERAL_VAL VARCHAR2(128) path 'value'
                          ) event
           WHERE MAP.EVENT_FACILITY_ID = P_EVENT_FACILITY_ID
             AND MAP.MAP_KEY = P_MAP_KEY
             AND PSI.ID = P_SCENARIO_INSTANCE_ID
             AND EVENT.LITERAL_VAL IS NOT NULL
        );

      V_SQL := 'BEGIN MERGE INTO P_ACCOUNT_ATTRIBUTE A USING (SELECT ' ||
               V_MERGE_QUERY || ' FROM DUAL) B ' ||
               ' ON (A.HOST_ID = B.HOST_ID AND A.ENTITY_ID = B.ENTITY_ID AND A.ACCOUNT_ID = B.ACCOUNT_ID AND A.ATTRIBUTE_ID = B.ATTRIBUTE_ID AND A.EFFECTIVE_DATE = B.EFFECTIVE_DATE)
               WHEN NOT MATCHED THEN
               INSERT (' || P_COL_AUTO_COL || V_MRG_SQL_INS_PART || ')' ||
               ' VALUES (' || P_COL_AUTO_VAL || V_MRG_SQL_INS_PART || ')' ||
               ' WHEN MATCHED THEN UPDATE SET ' || RTRIM(V_MRG_SQL_UPD_PART, ',') ||
               '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (' || P_SCENARIO_INSTANCE_ID ||
               ', ''Run successfully: Account attribute Inserted/updated: '' || ''' || V_KEY_VAL || '''); END;';

      RETURN V_SQL;

   EXCEPTION
      WHEN OTHERS THEN
         RETURN NULL;
   END;

   -- Process old instances in parallel
   PROCEDURE SP_PROCESS_OLD_INSTANCES_PARALLEL (P_SCENARIO_ID  P_SCENARIO.SCENARIO_ID%TYPE,
                                                P_USER_ID      VARCHAR2)
   IS
      TYPE T_INSTANCE_IDS IS TABLE OF NUMBER;
      V_INSTANCE_IDS T_INSTANCE_IDS;
      V_RESULT_LAUNCH_EVENT NUMBER;

   BEGIN
      -- Get old instances that need event processing
      SELECT PSI.ID
        BULK COLLECT INTO V_INSTANCE_IDS
        FROM P_SCENARIO_INSTANCE PSI
             INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
       WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
         AND PSI.EVENTS_LAUNCH_STATUS = 'W'
         AND ROWNUM <= 1000; -- Limit to prevent memory issues

      -- Process in parallel using FORALL
      FOR i IN 1..V_INSTANCE_IDS.COUNT LOOP
         BEGIN
            V_RESULT_LAUNCH_EVENT := FN_LAUNCH_SCEN_EVENT_OPTIMIZED(V_INSTANCE_IDS(i), P_USER_ID);
         EXCEPTION
            WHEN OTHERS THEN
               sp_error_log('', P_USER_ID, 'DBSERVER',
                           'PKG_ALERT_OPTIMIZED.SP_PROCESS_OLD_INSTANCES_PARALLEL -> Error for instance ' || V_INSTANCE_IDS(i),
                           SQLCODE, SQLERRM);
         END;
      END LOOP;

      COMMIT;

   EXCEPTION
      WHEN OTHERS THEN
         sp_error_log('', P_USER_ID, 'DBSERVER',
                     'PKG_ALERT_OPTIMIZED.SP_PROCESS_OLD_INSTANCES_PARALLEL -> Error for scenario ' || P_SCENARIO_ID,
                     SQLCODE, SQLERRM);
   END;

   -- Wrapper procedure to use optimized version
   PROCEDURE SP_PROCESS_SCENARIO (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                  P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                  P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                  P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL)
   IS
   BEGIN
      -- Use parallel processing for better performance
      SP_PROCESS_SCENARIO_PARALLEL(P_SCENARIO_ID, P_USER_ID, P_QUERY_TEXT, P_SCENARIO_SCHEDULE_ID);
   END;

   -- Optimized version of FN_LAUNCH_SCEN_EVENT
   FUNCTION FN_LAUNCH_SCEN_EVENT (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                  P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
   RETURN NUMBER
   IS
   BEGIN
      -- Use optimized version
      RETURN FN_LAUNCH_SCEN_EVENT_OPTIMIZED(P_SCENARIO_INSTANCE_ID, P_USER_ID);
   END;

   -- Cache management procedures
   PROCEDURE SP_CLEAR_CACHE
   IS
   BEGIN
      G_EVENT_MAPPING_CACHE.DELETE;
      G_COL_AUTO_CACHE.DELETE;
   END;

   PROCEDURE SP_WARM_CACHE (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE DEFAULT NULL)
   IS
      CURSOR C_PROGRAMS IS
         SELECT DISTINCT PEF.PROGRAM_ID
           FROM P_SCENARIO_EVENT_FACILITY PEF
                INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
          WHERE P_SCENARIO_ID IS NULL OR MAP.SCENARIO_ID = P_SCENARIO_ID;

   BEGIN
      -- Pre-load column auto cache for all programs
      FOR rec IN C_PROGRAMS LOOP
         IF NOT G_COL_AUTO_CACHE.EXISTS(rec.PROGRAM_ID) THEN
            G_COL_AUTO_CACHE(rec.PROGRAM_ID) := PKG_ALERT.FN_GET_COL_AUTO(rec.PROGRAM_ID);
         END IF;
      END LOOP;
   END;

   -- Performance monitoring function
   FUNCTION FN_GET_PERFORMANCE_STATS (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE DEFAULT NULL)
   RETURN VARCHAR2
   IS
      V_STATS VARCHAR2(4000);
      V_TOTAL_INSTANCES NUMBER;
      V_AVG_DURATION NUMBER;
      V_LAST_RUN_DATE DATE;

   BEGIN
      SELECT COUNT(*) AS TOTAL_INSTANCES,
             AVG(LAST_RUN_DURATION_SECS) AS AVG_DURATION,
             MAX(LAST_RUN_DATE) AS LAST_RUN_DATE
        INTO V_TOTAL_INSTANCES, V_AVG_DURATION, V_LAST_RUN_DATE
        FROM P_SCENARIO_SYSTEM SS
             INNER JOIN P_SCENARIO_INSTANCE SI ON (SS.SCENARIO_ID = SI.SCENARIO_ID)
       WHERE P_SCENARIO_ID IS NULL OR SS.SCENARIO_ID = P_SCENARIO_ID;

      V_STATS := 'Performance Stats:' || CHR(10) ||
                 'Total Instances: ' || V_TOTAL_INSTANCES || CHR(10) ||
                 'Average Duration (secs): ' || ROUND(V_AVG_DURATION, 2) || CHR(10) ||
                 'Last Run: ' || TO_CHAR(V_LAST_RUN_DATE, 'DD-MON-YYYY HH24:MI:SS') || CHR(10) ||
                 'Cache Size - Events: ' || G_EVENT_MAPPING_CACHE.COUNT || CHR(10) ||
                 'Cache Size - Col Auto: ' || G_COL_AUTO_CACHE.COUNT;

      RETURN V_STATS;

   EXCEPTION
      WHEN OTHERS THEN
         RETURN 'Error retrieving performance stats: ' || SQLERRM;
   END;

   -- Wrapper functions for backward compatibility - delegate to original package
   FUNCTION FN_GET_QUERY_TEXT (P_ID P_SCENARIO.SCENARIO_ID%TYPE) RETURN CLOB
   IS
   BEGIN
      RETURN PKG_ALERT.FN_GET_QUERY_TEXT(P_ID);
   END;

   FUNCTION FN_GET_CUR (P_SQLTEXT CLOB) RETURN SYS_REFCURSOR
   IS
   BEGIN
      RETURN PKG_ALERT.FN_GET_CUR(P_SQLTEXT);
   END;

   PROCEDURE PROC_GET_CUR
   IS
   BEGIN
      PKG_ALERT.PROC_GET_CUR();
   END;

   FUNCTION FN_GET_COUNT_FOR_QUERY (P_ID P_SCENARIO.SCENARIO_ID%TYPE, P_EXTRA_CONDS VARCHAR2 DEFAULT NULL) RETURN NUMBER
   IS
   BEGIN
      RETURN PKG_ALERT.FN_GET_COUNT_FOR_QUERY(P_ID, P_EXTRA_CONDS);
   END;

   FUNCTION FN_GET_MOVEMENT_ROWS (P_CURSOR IN SYS_REFCURSOR) RETURN T_MOVEMENT_TAB PIPELINED
   IS
   BEGIN
      FOR rec IN (SELECT * FROM TABLE(PKG_ALERT.FN_GET_MOVEMENT_ROWS(P_CURSOR))) LOOP
         PIPE ROW(rec);
      END LOOP;
      RETURN;
   END;

   FUNCTION FN_GET_MATCH_ROWS (P_CURSOR IN SYS_REFCURSOR) RETURN T_MATCH_TAB PIPELINED
   IS
   BEGIN
      FOR rec IN (SELECT * FROM TABLE(PKG_ALERT.FN_GET_MATCH_ROWS(P_CURSOR))) LOOP
         PIPE ROW(rec);
      END LOOP;
      RETURN;
   END;

   FUNCTION FN_GET_P_SCENARIO_COUNT_ROWS (P_CURSOR IN SYS_REFCURSOR) RETURN T_P_SCENARIO_COUNTS_TAB PIPELINED
   IS
   BEGIN
      FOR rec IN (SELECT * FROM TABLE(PKG_ALERT.FN_GET_P_SCENARIO_COUNT_ROWS(P_CURSOR))) LOOP
         PIPE ROW(rec);
      END LOOP;
      RETURN;
   END;

   FUNCTION FN_GET_SCENARIO_ACCESS (P_SCENARIO_ID P_NOTIFY_SCENARIO.SCENARIO_ID%TYPE, P_HOST_ID P_NOTIFY_SCENARIO.HOST_ID%TYPE, P_ROLE_ID P_NOTIFY_SCENARIO.ROLE_ID%TYPE, P_ENTITY_ID P_NOTIFY_SCENARIO.ENTITY_ID%TYPE) RETURN VARCHAR
   IS
   BEGIN
      RETURN PKG_ALERT.FN_GET_SCENARIO_ACCESS(P_SCENARIO_ID, P_HOST_ID, P_ROLE_ID, P_ENTITY_ID);
   END;

   PROCEDURE PROC_POPULATE_SCENARIO_COUNTS(vSystemFlag VARCHAR2)
   IS
   BEGIN
      PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS(vSystemFlag);
   END;

   PROCEDURE INS_UPD_P_SCENARIO_COUNTS (P_SCENARIO_COUNT_REC P_SCENARIO_COUNTS%ROWTYPE, P_SCENARIO_COUNTS_TAB_BKP T_P_SCENARIO_COUNTS_TAB DEFAULT NULL)
   IS
   BEGIN
      PKG_ALERT.INS_UPD_P_SCENARIO_COUNTS(P_SCENARIO_COUNT_REC, P_SCENARIO_COUNTS_TAB_BKP);
   END;

   FUNCTION FN_CONV_INTERVAL_TO_SECS (P_IN_INTERVAL INTERVAL DAY TO SECOND) RETURN NUMBER
   IS
   BEGIN
      RETURN PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(P_IN_INTERVAL);
   END;

   FUNCTION FN_CONV_SECS_TO_DUR_STRING (P_IN_SECS NUMBER) RETURN VARCHAR2
   IS
   BEGIN
      RETURN PKG_ALERT.FN_CONV_SECS_TO_DUR_STRING(P_IN_SECS);
   END;

   FUNCTION FN_TEST_START_END_TIME (P_TEST_DATETIME DATE, P_START_TIME VARCHAR2, P_END_TIME VARCHAR2) RETURN VARCHAR
   IS
   BEGIN
      RETURN PKG_ALERT.FN_TEST_START_END_TIME(P_TEST_DATETIME, P_START_TIME, P_END_TIME);
   END;

   PROCEDURE PRC_EXEC_QUERY (p_QUERY_TEXT VARCHAR2, p_FILTER VARCHAR2, p_ORDER VARCHAR2, p_ROW_BEGIN NUMBER, p_ROW_END NUMBER, p_ASC_DESC VARCHAR2, p_CUR_RES IN OUT SYS_REFCURSOR, p_Count_Rows OUT NUMBER, p_Query_String OUT VARCHAR2)
   IS
   BEGIN
      PKG_ALERT.PRC_EXEC_QUERY(p_QUERY_TEXT, p_FILTER, p_ORDER, p_ROW_BEGIN, p_ROW_END, p_ASC_DESC, p_CUR_RES, p_Count_Rows, p_Query_String);
   END;

   FUNCTION FN_GET_SCENARIO_INSTANCE_ROW (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE, P_SCHEDULED_QUERY P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL, P_USER_ID VARCHAR2 DEFAULT CONST_SYS_USER) RETURN TAB_SCENARIO_INSTANCE PIPELINED
   IS
   BEGIN
      FOR rec IN (SELECT * FROM TABLE(PKG_ALERT.FN_GET_SCENARIO_INSTANCE_ROW(P_SCENARIO_ID, P_SCHEDULED_QUERY, P_USER_ID))) LOOP
         PIPE ROW(rec);
      END LOOP;
      RETURN;
   END;

   PROCEDURE SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID P_SCENARIO_INSTANCE.ID%TYPE, P_LOG_TEXT P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE, P_LOG_USER P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
   IS
   BEGIN
      PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(P_SCENARIO_INSTANCE_ID, P_LOG_TEXT, P_LOG_USER);
   END;

   PROCEDURE SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID P_SCENARIO_INSTANCE.ID%TYPE, PV_NEW_STATUS P_SCENARIO_INSTANCE.STATUS%TYPE, P_LOG_TEXT P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE, P_LOG_USER P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE, P_EVENT_STATUS P_SCENARIO_INSTANCE.EVENTS_LAUNCH_STATUS%TYPE DEFAULT NULL)
   IS
   BEGIN
      PKG_ALERT.SP_UPD_INSTANCE_STATUS(P_SCENARIO_INSTANCE_ID, PV_NEW_STATUS, P_LOG_TEXT, P_LOG_USER, P_EVENT_STATUS);
   END;

   PROCEDURE SP_UPD_SCENARIO_INSTANCE (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE, P_USER_ID P_SCENARIO_INSTANCE.RESOLVED_BY_USER%TYPE DEFAULT CONST_SYS_USER)
   IS
   BEGIN
      PKG_ALERT.SP_UPD_SCENARIO_INSTANCE(P_SCENARIO_ID, P_USER_ID);
   END;

   FUNCTION FN_GET_SCHEDULED_QUERY (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE, P_SCHEDULED_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE) RETURN CLOB
   IS
   BEGIN
      RETURN PKG_ALERT.FN_GET_SCHEDULED_QUERY(P_SCENARIO_ID, P_SCHEDULED_ID);
   END;

   FUNCTION FN_GET_SCN_ATTR_VAL (P_ATTRIBUTE_NAME VARCHAR2, P_SCENARIO_INSTANCE_ID P_SCENARIO_INSTANCE.ID%TYPE) RETURN VARCHAR2
   IS
   BEGIN
      RETURN PKG_ALERT.FN_GET_SCN_ATTR_VAL(P_ATTRIBUTE_NAME, P_SCENARIO_INSTANCE_ID);
   END;

   FUNCTION FN_GET_SCN_ATTR_TYPE (P_ATTRIBUTE_NAME VARCHAR2, P_SCENARIO_INSTANCE_ID P_SCENARIO_INSTANCE.ID%TYPE) RETURN VARCHAR2
   IS
   BEGIN
      RETURN PKG_ALERT.FN_GET_SCN_ATTR_TYPE(P_ATTRIBUTE_NAME, P_SCENARIO_INSTANCE_ID);
   END;

   PROCEDURE SP_UPD_SCEN_INSTANCE_COUNTS (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE)
   IS
   BEGIN
      PKG_ALERT.SP_UPD_SCEN_INSTANCE_COUNTS(P_SCENARIO_ID);
   END;

END;
/
